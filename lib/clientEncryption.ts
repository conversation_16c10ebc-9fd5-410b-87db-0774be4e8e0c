/**
 * Client-side encryption utilities for sensitive data storage
 * Uses Web Crypto API for secure encryption of images and data
 */

export interface EncryptedData {
  encryptedData: string;
  iv: string;
  salt: string;
}

// Encryption configuration
const ALGORITHM = 'AES-GCM';
const KEY_LENGTH = 256;
const IV_LENGTH = 12; // 96 bits for GCM
const SALT_LENGTH = 16; // 128 bits

/**
 * Generates a cryptographic key from password using PBKDF2
 */
async function deriveKey(password: string, salt: Uint8Array): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const passwordBuffer = encoder.encode(password);
  
  // Import password as key material
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    passwordBuffer,
    'PBKDF2',
    false,
    ['deriveKey']
  );
  
  // Derive AES key using PBKDF2
  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: salt,
      iterations: 100000, // High iteration count for security
      hash: 'SHA-256'
    },
    keyMaterial,
    {
      name: AL<PERSON><PERSON><PERSON>H<PERSON>,
      length: KEY_LENGTH
    },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Generates a secure password for encryption based on user session
 */
function generateUserPassword(userId: string): string {
  // Generate deterministic but secure password from user ID
  // In production, this should be derived from user's actual password or a secure key
  const basePassword = `crybaby_${userId}_encryption_key`;
  
  // Add some entropy from browser fingerprint
  const browserFingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width,
    screen.height,
    new Date().getTimezoneOffset()
  ].join('|');
  
  return `${basePassword}_${btoa(browserFingerprint).substring(0, 16)}`;
}

/**
 * Encrypts data using AES-GCM
 */
export async function encryptData(data: string, userId: string): Promise<EncryptedData> {
  try {
    // Check if Web Crypto API is available
    if (!crypto.subtle) {
      throw new Error('Web Crypto API not available');
    }
    
    // Generate random salt and IV
    const salt = crypto.getRandomValues(new Uint8Array(SALT_LENGTH));
    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));
    
    // Derive encryption key
    const password = generateUserPassword(userId);
    const key = await deriveKey(password, salt);
    
    // Encrypt data
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    
    const encryptedBuffer = await crypto.subtle.encrypt(
      {
        name: ALGORITHM,
        iv: iv
      },
      key,
      dataBuffer
    );
    
    // Convert to base64 for storage
    const encryptedArray = new Uint8Array(encryptedBuffer);
    const encryptedData = btoa(String.fromCharCode(...encryptedArray));
    const ivString = btoa(String.fromCharCode(...iv));
    const saltString = btoa(String.fromCharCode(...salt));
    
    return {
      encryptedData,
      iv: ivString,
      salt: saltString
    };
  } catch (error) {
    console.error('Encryption failed:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypts data using AES-GCM
 */
export async function decryptData(
  encryptedData: EncryptedData,
  userId: string
): Promise<string> {
  try {
    // Check if Web Crypto API is available
    if (!crypto.subtle) {
      throw new Error('Web Crypto API not available');
    }
    
    // Convert from base64
    const encryptedBuffer = Uint8Array.from(
      atob(encryptedData.encryptedData),
      c => c.charCodeAt(0)
    );
    const iv = Uint8Array.from(atob(encryptedData.iv), c => c.charCodeAt(0));
    const salt = Uint8Array.from(atob(encryptedData.salt), c => c.charCodeAt(0));
    
    // Derive decryption key
    const password = generateUserPassword(userId);
    const key = await deriveKey(password, salt);
    
    // Decrypt data
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: ALGORITHM,
        iv: iv
      },
      key,
      encryptedBuffer
    );
    
    // Convert back to string
    const decoder = new TextDecoder();
    return decoder.decode(decryptedBuffer);
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Checks if encryption is available in the current environment
 */
export function isEncryptionAvailable(): boolean {
  return !!(
    typeof crypto !== 'undefined' &&
    crypto.subtle &&
    typeof crypto.subtle.encrypt === 'function' &&
    typeof crypto.subtle.decrypt === 'function' &&
    typeof crypto.getRandomValues === 'function'
  );
}

/**
 * Encrypts base64 image data for secure storage
 */
export async function encryptImage(
  base64Image: string,
  userId: string
): Promise<EncryptedData> {
  if (!isEncryptionAvailable()) {
    console.warn('Encryption not available, storing image unencrypted');
    // Return fake encrypted data that's actually just base64 encoded
    return {
      encryptedData: btoa(base64Image),
      iv: '',
      salt: ''
    };
  }
  
  try {
    return await encryptData(base64Image, userId);
  } catch (error) {
    console.error('Image encryption failed:', error);
    // Fallback to base64 encoding
    return {
      encryptedData: btoa(base64Image),
      iv: '',
      salt: ''
    };
  }
}

/**
 * Decrypts base64 image data from secure storage
 */
export async function decryptImage(
  encryptedImage: EncryptedData,
  userId: string
): Promise<string> {
  if (!isEncryptionAvailable() || !encryptedImage.iv || !encryptedImage.salt) {
    // Handle fallback case (base64 encoded data)
    try {
      return atob(encryptedImage.encryptedData);
    } catch {
      // If base64 decode fails, return as-is
      return encryptedImage.encryptedData;
    }
  }
  
  try {
    return await decryptData(encryptedImage, userId);
  } catch (error) {
    console.error('Image decryption failed:', error);
    // Try fallback decoding
    try {
      return atob(encryptedImage.encryptedData);
    } catch {
      throw new Error('Failed to decrypt image data');
    }
  }
}

/**
 * Securely wipes sensitive data from memory (best effort)
 */
export function secureWipe(data: string | Uint8Array): void {
  if (typeof data === 'string') {
    // For strings, we can't directly overwrite memory, but we can help GC
    data = '';
  } else {
    // For arrays, overwrite with random data
    if (crypto.getRandomValues) {
      crypto.getRandomValues(data);
    } else {
      // Fallback - fill with zeros
      data.fill(0);
    }
  }
}

/**
 * Generates a secure hash of data for integrity verification
 */
export async function generateHash(data: string): Promise<string> {
  if (!crypto.subtle) {
    // Fallback simple hash (not cryptographically secure)
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }
  
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
  const hashArray = new Uint8Array(hashBuffer);
  
  return Array.from(hashArray, byte => 
    byte.toString(16).padStart(2, '0')
  ).join('');
}

/**
 * Verifies data integrity using hash comparison
 */
export async function verifyIntegrity(
  data: string,
  expectedHash: string
): Promise<boolean> {
  try {
    const actualHash = await generateHash(data);
    return actualHash === expectedHash;
  } catch (error) {
    console.error('Hash verification failed:', error);
    return false;
  }
}