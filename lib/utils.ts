import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { clientEnv, validateClientEnv, validateServerEnv } from './envConfig';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Environment variable checks using centralized config
export const hasEnvVars = !!(
  clientEnv.supabase.url &&
  clientEnv.supabase.anonKey
);

// Server-side only function for rate limiting validation
export function validateRateLimitingEnvironment(): void {
  // This function should only be called on server-side
  if (typeof window !== 'undefined') {
    throw new Error('validateRateLimitingEnvironment called on client side');
  }
  
  try {
    validateClientEnv();
    validateServerEnv();
  } catch (error) {
    throw new Error(`Rate limiting environment validation failed: ${error}`);
  }
}
