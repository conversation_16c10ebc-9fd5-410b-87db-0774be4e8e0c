interface SanitizationResult {
  sanitized: string;
  warnings: string[];
  isValid: boolean;
}

interface ValidationOptions {
  maxLength?: number;
  allowHTML?: boolean;
  strictMode?: boolean;
}

/**
 * Comprehensive input sanitization with enhanced security
 */
export function sanitizeInput(
  input: string,
  options: ValidationOptions = {}
): SanitizationResult {
  const {
    maxLength = 4000,
    allowHTML = false,
    strictMode = true
  } = options;

  const warnings: string[] = [];
  let sanitized = input;
  let isValid = true;

  // Type validation
  if (typeof input !== 'string') {
    return {
      sanitized: '',
      warnings: ['Invalid input type - must be string'],
      isValid: false
    };
  }

  // Trim whitespace
  sanitized = sanitized.trim();

  // Length validation
  if (sanitized.length === 0) {
    warnings.push('Empty input after sanitization');
    isValid = false;
  }

  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
    warnings.push(`Input truncated to ${maxLength} characters`);
  }

  // Enhanced harmful pattern detection
  const harmfulPatterns = [
    // Script injection patterns
    /<script[^>]*>/gi,
    /<\/script>/gi,
    /javascript\s*:/gi,
    /vbscript\s*:/gi,
    /data\s*:/gi,
    /livescript\s*:/gi,
    
    // Event handlers
    /on\w+\s*=/gi,
    
    // HTML injection patterns
    /<iframe[^>]*>/gi,
    /<object[^>]*>/gi,
    /<embed[^>]*>/gi,
    /<form[^>]*>/gi,
    /<input[^>]*>/gi,
    
    // SQL injection patterns (basic)
    /(\b(union|select|insert|delete|update|drop|create|alter|exec|execute)\b)/gi,
    
    // Command injection patterns
    /[\$`]/g,
    /\|\s*(rm|del|format|shutdown)/gi,
    
    // Path traversal
    /\.\.\//g,
    /\.\.\\/g,
    
    // Null bytes and control characters
    /\x00/g,
    /[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]/g
  ];

  let patternFound = false;
  for (const pattern of harmfulPatterns) {
    if (pattern.test(sanitized)) {
      sanitized = sanitized.replace(pattern, '');
      patternFound = true;
    }
  }

  if (patternFound) {
    warnings.push('Removed potentially harmful content');
    if (strictMode) {
      isValid = false;
    }
  }

  // HTML sanitization if not allowed
  if (!allowHTML) {
    const htmlEntities: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;'
    };

    const originalLength = sanitized.length;
    sanitized = sanitized.replace(/[&<>"'/]/g, (char) => htmlEntities[char]);
    
    if (sanitized.length !== originalLength) {
      warnings.push('HTML entities encoded for security');
    }
  }

  // Unicode normalization to prevent bypasses
  try {
    sanitized = sanitized.normalize('NFKC');
  } catch {
    warnings.push('Unicode normalization failed');
    isValid = false;
  }

  // Additional strict mode validations
  if (strictMode) {
    // Check for suspicious character combinations
    const suspiciousPatterns = [
      /\w+\s*=\s*['"]/g, // Potential attribute assignments
      /[\(\)\[\]{}]/g,   // Brackets that could indicate code
      /[<>]/g           // Angle brackets after HTML encoding should be gone
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(sanitized)) {
        warnings.push('Input contains suspicious patterns');
        isValid = false;
        break;
      }
    }
  }

  // Final whitespace normalization
  sanitized = sanitized.replace(/\s+/g, ' ').trim();

  return {
    sanitized,
    warnings,
    isValid
  };
}

/**
 * Sanitizes image prompts with specific validation for AI processing
 */
export function sanitizeImagePrompt(prompt: string): SanitizationResult {
  const result = sanitizeInput(prompt, {
    maxLength: 32000,
    allowHTML: false,
    strictMode: true
  });

  // Additional image prompt specific validations
  const imageSpecificPatterns = [
    /\b(nsfw|explicit|nude|naked|porn|sexual|adult)\b/gi,
    /\b(violence|blood|gore|death|kill|murder)\b/gi,
    /\b(hate|racist|nazi|terrorism|bomb|weapon)\b/gi
  ];

  let contentWarning = false;
  for (const pattern of imageSpecificPatterns) {
    if (pattern.test(result.sanitized)) {
      result.warnings.push('Content may violate usage policies');
      contentWarning = true;
      break;
    }
  }

  // For image prompts, flag but don't invalidate content warnings
  // Let the AI service handle policy enforcement
  if (contentWarning && result.isValid) {
    result.warnings.push('Content flagged for review but processing will continue');
  }

  return result;
}

/**
 * Sanitizes text input for general text processing
 */
export function sanitizeTextInput(text: string): SanitizationResult {
  return sanitizeInput(text, {
    maxLength: 4000,
    allowHTML: false,
    strictMode: true
  });
}

/**
 * Sanitizes email addresses
 */
export function sanitizeEmail(email: string): SanitizationResult {
  const result = sanitizeInput(email, {
    maxLength: 254, // RFC 5321 limit
    allowHTML: false,
    strictMode: true
  });

  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (result.sanitized && !emailRegex.test(result.sanitized)) {
    result.warnings.push('Invalid email format');
    result.isValid = false;
  }

  return result;
}

/**
 * Sanitizes file names to prevent path traversal and other attacks
 */
export function sanitizeFileName(fileName: string): SanitizationResult {
  const result = sanitizeInput(fileName, {
    maxLength: 255,
    allowHTML: false,
    strictMode: true
  });

  // Remove path traversal attempts
  result.sanitized = result.sanitized.replace(/[/\\:*?"<>|]/g, '_');
  
  // Remove leading dots and spaces
  result.sanitized = result.sanitized.replace(/^[\.\s]+/, '');
  
  // Ensure not empty after sanitization
  if (!result.sanitized) {
    result.sanitized = 'sanitized_file';
    result.warnings.push('File name was completely sanitized, using default name');
  }

  return result;
}