/**
 * File validation and security utilities
 * Includes magic number validation to prevent malicious file uploads
 */

interface FileValidationResult {
  isValid: boolean;
  mimeType: string | null;
  detectedType: string | null;
  warnings: string[];
  errors: string[];
}

// Magic number signatures for supported image formats
const MAGIC_NUMBERS = {
  jpeg: [
    [0xFF, 0xD8, 0xFF, 0xE0], // JPEG JFIF
    [0xFF, 0xD8, 0xFF, 0xE1], // JPEG EXIF
    [0xFF, 0xD8, 0xFF, 0xE2], // JPEG
    [0xFF, 0xD8, 0xFF, 0xE3], // JPEG
    [0xFF, 0xD8, 0xFF, 0xDB], // JPEG raw
  ],
  png: [
    [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A] // PNG
  ],
  webp: [
    [0x52, 0x49, 0x46, 0x46, null, null, null, null, 0x57, 0x45, 0x42, 0x50] // WEBP (null = any byte)
  ],
  gif: [
    [0x47, 0x49, 0x46, 0x38, 0x37, 0x61], // GIF87a
    [0x47, 0x49, 0x46, 0x38, 0x39, 0x61]  // GIF89a
  ],
  bmp: [
    [0x42, 0x4D] // BMP
  ],
  tiff: [
    [0x49, 0x49, 0x2A, 0x00], // TIFF little endian
    [0x4D, 0x4D, 0x00, 0x2A]  // TIFF big endian
  ]
};

// Allowed MIME types
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp'
];

// Maximum file sizes (in bytes)
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_IMAGE_DIMENSION = 4096; // 4096px max width/height

/**
 * Detects file type based on magic number signature
 */
function detectFileType(buffer: ArrayBuffer): string | null {
  const bytes = new Uint8Array(buffer.slice(0, 12)); // Read first 12 bytes for magic number detection
  
  for (const [type, signatures] of Object.entries(MAGIC_NUMBERS)) {
    for (const signature of signatures) {
      let matches = true;
      for (let i = 0; i < signature.length; i++) {
        if (signature[i] !== null && bytes[i] !== signature[i]) {
          matches = false;
          break;
        }
      }
      if (matches) {
        return type;
      }
    }
  }
  
  return null;
}

/**
 * Validates file buffer against security checks
 */
function validateFileBuffer(buffer: ArrayBuffer): { warnings: string[], errors: string[] } {
  const warnings: string[] = [];
  const errors: string[] = [];
  const bytes = new Uint8Array(buffer);
  
  // Check for embedded scripts or suspicious patterns
  const suspiciousPatterns = [
    // Script tags
    [0x3C, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74], // <script
    [0x3C, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54], // <SCRIPT
    
    // PHP tags
    [0x3C, 0x3F, 0x70, 0x68, 0x70], // <?php
    [0x3C, 0x3F, 0x50, 0x48, 0x50], // <?PHP
    
    // JavaScript
    [0x6A, 0x61, 0x76, 0x61, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74], // javascript
    
    // Executable headers
    [0x4D, 0x5A], // MZ (DOS/Windows executable)
    [0x7F, 0x45, 0x4C, 0x46], // ELF (Linux executable)
  ];
  
  for (const pattern of suspiciousPatterns) {
    for (let i = 0; i <= bytes.length - pattern.length; i++) {
      let matches = true;
      for (let j = 0; j < pattern.length; j++) {
        if (bytes[i + j] !== pattern[j]) {
          matches = false;
          break;
        }
      }
      if (matches) {
        errors.push('File contains suspicious executable content');
        break;
      }
    }
    if (errors.length > 0) break;
  }
  
  // Check file size
  if (buffer.byteLength > MAX_FILE_SIZE) {
    errors.push(`File size ${buffer.byteLength} exceeds maximum allowed size ${MAX_FILE_SIZE}`);
  }
  
  // Check for extremely small files (likely corrupted or fake)
  if (buffer.byteLength < 100) {
    warnings.push('File is unusually small and may be corrupted');
  }
  
  // Check for null bytes in unexpected places (after first 50 bytes)
  const dataSection = bytes.slice(50);
  let nullByteCount = 0;
  for (const byte of dataSection) {
    if (byte === 0) nullByteCount++;
  }
  
  if (nullByteCount > dataSection.length * 0.1) { // More than 10% null bytes
    warnings.push('File contains unusually high number of null bytes');
  }
  
  return { warnings, errors };
}

/**
 * Validates uploaded file for security and type compliance
 */
export async function validateUploadedFile(file: File): Promise<FileValidationResult> {
  const warnings: string[] = [];
  const errors: string[] = [];
  
  try {
    // Basic file object validation
    if (!file || !(file instanceof File)) {
      return {
        isValid: false,
        mimeType: null,
        detectedType: null,
        warnings: [],
        errors: ['Invalid file object']
      };
    }
    
    // Validate MIME type
    if (!ALLOWED_MIME_TYPES.includes(file.type)) {
      errors.push(`MIME type ${file.type} is not allowed. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}`);
    }
    
    // Validate file name
    if (file.name.length > 255) {
      errors.push('File name is too long');
    }
    
    // Check for suspicious file extensions
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.php', '.asp', '.jsp', '.js', '.vbs'];
    const fileName = file.name.toLowerCase();
    for (const ext of suspiciousExtensions) {
      if (fileName.includes(ext)) {
        errors.push(`File name contains suspicious extension: ${ext}`);
        break;
      }
    }
    
    // Read file buffer for magic number validation
    const buffer = await file.arrayBuffer();
    
    // Detect actual file type using magic numbers
    const detectedType = detectFileType(buffer);
    
    if (!detectedType) {
      errors.push('Unable to detect valid image file type from file content');
      return {
        isValid: false,
        mimeType: file.type,
        detectedType: null,
        warnings,
        errors
      };
    }
    
    // Verify MIME type matches detected type
    const expectedMimeType = `image/${detectedType === 'jpeg' ? 'jpeg' : detectedType}`;
    if (file.type !== expectedMimeType) {
      if (ALLOWED_MIME_TYPES.includes(expectedMimeType)) {
        warnings.push(`MIME type mismatch: reported ${file.type}, detected ${expectedMimeType}`);
      } else {
        errors.push(`Detected file type ${detectedType} is not allowed`);
      }
    }
    
    // Validate file buffer for security issues
    const bufferValidation = validateFileBuffer(buffer);
    warnings.push(...bufferValidation.warnings);
    errors.push(...bufferValidation.errors);
    
    return {
      isValid: errors.length === 0,
      mimeType: file.type,
      detectedType,
      warnings,
      errors
    };
    
  } catch (error) {
    return {
      isValid: false,
      mimeType: file.type || null,
      detectedType: null,
      warnings,
      errors: [`File validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

/**
 * Sanitizes file name to prevent path traversal and other attacks
 */
export function sanitizeFileName(fileName: string): string {
  // Remove path separators and dangerous characters
  let sanitized = fileName.replace(/[/\\:*?"<>|]/g, '_');
  
  // Remove leading/trailing dots and spaces
  sanitized = sanitized.replace(/^[\.\s]+|[\.\s]+$/g, '');
  
  // Limit length
  if (sanitized.length > 100) {
    const ext = sanitized.substring(sanitized.lastIndexOf('.'));
    sanitized = sanitized.substring(0, 100 - ext.length) + ext;
  }
  
  // Ensure not empty
  if (!sanitized) {
    sanitized = 'sanitized_file';
  }
  
  return sanitized;
}

/**
 * Additional validation for image dimensions using canvas
 */
export async function validateImageDimensions(file: File): Promise<{ isValid: boolean; width?: number; height?: number; error?: string }> {
  return new Promise((resolve) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      
      if (img.width > MAX_IMAGE_DIMENSION || img.height > MAX_IMAGE_DIMENSION) {
        resolve({
          isValid: false,
          width: img.width,
          height: img.height,
          error: `Image dimensions ${img.width}x${img.height} exceed maximum allowed ${MAX_IMAGE_DIMENSION}x${MAX_IMAGE_DIMENSION}`
        });
      } else {
        resolve({
          isValid: true,
          width: img.width,
          height: img.height
        });
      }
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      resolve({
        isValid: false,
        error: 'Failed to load image for dimension validation'
      });
    };
    
    img.src = url;
  });
}