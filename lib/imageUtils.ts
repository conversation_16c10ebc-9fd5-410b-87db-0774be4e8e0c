import sharp from 'sharp';
import fs from 'fs';

/**
 * Converts SVG to PNG buffer with specified dimensions
 */
export async function svgToPng(svgPath: string, width: number, height: number): Promise<Buffer> {
  try {
    console.log(`Converting SVG to PNG: ${svgPath}, dimensions: ${width}x${height}`);
    
    // Read SVG file
    const svgBuffer = await fs.promises.readFile(svgPath);
    
    // Convert SVG to PNG using Sharp
    const pngBuffer = await sharp(svgBuffer)
      .resize(width, height, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
      })
      .png()
      .toBuffer();
    
    console.log(`SVG converted to PNG successfully. Output size: ${pngBuffer.length} bytes`);
    return pngBuffer;
  } catch (error) {
    console.error('Error converting SVG to PNG:', error);
    throw new Error(`Failed to convert SVG to PNG: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Adds logo overlay centered at the bottom of an image
 */
export async function addLogoOverlay(
  imageBuffer: Buffer,
  logoPath: string,
  options: {
    logoSizePercent?: number; // Logo size as percentage of image width
    paddingPercent?: number;  // Padding as percentage of image width
    opacity?: number;         // Logo opacity (0-1)
  } = {}
): Promise<Buffer> {
  try {
    const {
      logoSizePercent = 12,  // 12% of image width
      paddingPercent = 3,    // 3% padding from edges
      opacity = 0.9          // 90% opacity
    } = options;

    console.log('Starting logo overlay process with options:', {
      logoSizePercent,
      paddingPercent,
      opacity,
      logoPath
    });

    // Get image metadata
    const imageMetadata = await sharp(imageBuffer).metadata();
    const imageWidth = imageMetadata.width || 1024;
    const imageHeight = imageMetadata.height || 1024;
    
    console.log(`Image dimensions: ${imageWidth}x${imageHeight}`);

    // Calculate logo dimensions based on extended logo aspect ratio (3647x476 ≈ 7.66:1)
    const logoWidth = Math.round(imageWidth * (logoSizePercent / 100));
    const logoHeight = Math.round(logoWidth / 7.66); // Maintain aspect ratio
    const padding = Math.round(imageWidth * (paddingPercent / 100));
    
    console.log(`Logo dimensions: ${logoWidth}x${logoHeight}px, Padding: ${padding}px`);

    // Convert SVG logo to PNG with calculated dimensions
    const logoPngBuffer = await svgToPng(logoPath, logoWidth, logoHeight);

    // Calculate logo position (centered horizontally at bottom with padding)
    const logoLeft = Math.round((imageWidth - logoWidth) / 2);
    const logoTop = imageHeight - logoHeight - padding;
    
    console.log(`Logo position: left=${logoLeft}, top=${logoTop}`);

    // Create logo overlay with opacity
    const { data: logoData, info: logoInfo } = await sharp(logoPngBuffer)
      .ensureAlpha()
      .raw()
      .toBuffer({ resolveWithObject: true });

    for (let i = 3; i < logoData.length; i += 4) {
      logoData[i] = Math.round(logoData[i] * opacity);
    }

    const logoOverlay = await sharp(logoData, { raw: logoInfo }).png().toBuffer();

    // Composite the logo onto the image
    const resultBuffer = await sharp(imageBuffer)
      .composite([{
        input: logoOverlay,
        left: logoLeft,
        top: logoTop,
        blend: 'over'
      }])
      .png()
      .toBuffer();

    console.log(`Logo overlay completed successfully. Result size: ${resultBuffer.length} bytes`);
    return resultBuffer;

  } catch (error) {
    console.error('Error adding logo overlay:', error);
    throw new Error(`Failed to add logo overlay: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Processes a base64 image string by adding logo overlay
 */
export async function processImageWithLogo(
  base64Image: string,
  logoPath: string,
  options?: {
    logoSizePercent?: number;
    paddingPercent?: number;
    opacity?: number;
  }
): Promise<string> {
  try {
    console.log('Processing base64 image with logo overlay');
    
    // Extract base64 data (remove data URL prefix if present)
    const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');
    
    console.log(`Input image buffer size: ${imageBuffer.length} bytes`);

    // Add logo overlay
    const processedBuffer = await addLogoOverlay(imageBuffer, logoPath, options);

    // Convert back to base64 data URL
    const resultBase64 = `data:image/png;base64,${processedBuffer.toString('base64')}`;
    
    console.log('Image processing with logo completed successfully');
    return resultBase64;

  } catch (error) {
    console.error('Error processing image with logo:', error);
    throw new Error(`Failed to process image with logo: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Optimizes image buffer with specified options
 */
export async function optimizeImage(
  buffer: Buffer,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
  } = {}
): Promise<Buffer> {
  const {
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 85,
    format = 'jpeg'
  } = options;

  console.log(`Optimizing image with options:`, { maxWidth, maxHeight, quality, format });

  let pipeline = sharp(buffer)
    .resize(maxWidth, maxHeight, {
      fit: 'inside',
      withoutEnlargement: true
    });

  switch (format) {
    case 'jpeg':
      pipeline = pipeline.jpeg({ quality });
      break;
    case 'png':
      pipeline = pipeline.png({ compressionLevel: 8 });
      break;
    case 'webp':
      pipeline = pipeline.webp({ quality });
      break;
  }

  const result = await pipeline.toBuffer();
  console.log(`Image optimization completed. Output size: ${result.length} bytes`);
  
  return result;
}
