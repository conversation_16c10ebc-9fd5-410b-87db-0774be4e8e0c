import { createClient } from '@supabase/supabase-js';
import { validateRateLimitingEnvironment } from './utils';
import { clientEnv, serverEnv } from './envConfig';

// Types for rate limiting responses
export interface RateLimitStatus {
  allowed: boolean;
  userRemaining: number;
  userLimit: number;
  globalRemaining: number;
  globalLimit: number;
  resetTime: string;
  reason?: string;
}

export interface GenerationLogEntry {
  userId: number;
  prompt: string;
  success: boolean;
  imageUrl?: string;
  errorMessage?: string;
}

// Create service-level Supabase client for rate limiting operations
function createServiceClient() {
  // Validate all required environment variables are present
  validateRateLimitingEnvironment();

  const supabaseUrl = clientEnv.supabase.url;
  const serviceKey = serverEnv.supabase.serviceKey;

  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

/**
 * Maps a Supabase UUID to a numeric user ID for rate limiting
 */
export async function getUserNumericId(supabaseUuid: string): Promise<number> {
  console.log('🔄 Mapping Supabase UUID to numeric ID:', supabaseUuid);
  
  try {
    const supabase = createServiceClient();
    
    const { data, error } = await supabase.rpc('get_or_create_numeric_user_id', {
      input_uuid: supabaseUuid
    });

    if (error) {
      console.error('❌ Error mapping user ID:', error);
      throw new Error(`Failed to map user ID: ${error.message}`);
    }

    console.log('✅ Mapped to numeric ID:', data);
    return data;
  } catch (error) {
    console.error('❌ Critical error in getUserNumericId:', error);
    throw error;
  }
}

/**
 * Checks if generation is allowed for a user (checks both user and global limits)
 */
export async function checkGenerationAllowed(userId: number): Promise<RateLimitStatus> {
  console.log('🔍 Checking generation limits for user:', userId);
  
  try {
    const supabase = createServiceClient();
    
    const { data, error } = await supabase.rpc('check_generation_allowed', {
      user_id_param: userId
    });

    if (error) {
      console.error('❌ Error checking generation limits:', error);
      throw new Error(`Failed to check limits: ${error.message}`);
    }

    console.log('📊 Rate limit status:', data);

    const status: RateLimitStatus = {
      allowed: data.allowed,
      userRemaining: data.user_remaining,
      userLimit: data.user_limit,
      globalRemaining: data.global_remaining,
      globalLimit: data.global_limit,
      resetTime: 'midnight UTC',
      reason: data.allowed ? undefined : (
        data.user_remaining <= 0 ? 'User daily limit exceeded' : 'Global daily limit exceeded'
      )
    };

    return status;
  } catch (error) {
    console.error('❌ Critical error in checkGenerationAllowed:', error);
    throw error;
  }
}

/**
 * Decrements both user and global limits atomically
 */
export async function decrementLimits(userId: number): Promise<boolean> {
  console.log('⬇️ Decrementing limits for user:', userId);
  
  try {
    const supabase = createServiceClient();
    
    const { data, error } = await supabase.rpc('decrement_both_limits', {
      user_id_param: userId
    });

    if (error) {
      console.error('❌ Error decrementing limits:', error);
      throw new Error(`Failed to decrement limits: ${error.message}`);
    }

    console.log('✅ Limits decremented successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Critical error in decrementLimits:', error);
    throw error;
  }
}

/**
 * Logs a generation attempt for audit and analytics
 */
export async function logGenerationAttempt(entry: GenerationLogEntry): Promise<void> {
  console.log('📝 Logging generation attempt for user:', entry.userId);
  
  try {
    const supabase = createServiceClient();
    
    const { error } = await supabase
      .from('cry_generation_logs')
      .insert({
        user_id: entry.userId,
        prompt: entry.prompt.substring(0, 1000), // Truncate very long prompts
        success: entry.success,
        image_url: entry.imageUrl,
        error_message: entry.errorMessage
      });

    if (error) {
      console.error('❌ Error logging generation attempt:', error);
      // Don't throw here - logging failures shouldn't break the main flow
    } else {
      console.log('✅ Generation attempt logged successfully');
    }
  } catch (error) {
    console.error('❌ Critical error in logGenerationAttempt:', error);
    // Don't throw here - logging failures shouldn't break the main flow
  }
}

/**
 * Gets current rate limit status for a user (for display purposes)
 */
export async function getRateLimitStatus(userId: number): Promise<RateLimitStatus> {
  return checkGenerationAllowed(userId);
}

/**
 * Atomic rate limiting function that checks and reserves a slot in one operation
 * This prevents race conditions between check and decrement
 */
export async function enforceRateLimitAtomic(supabaseUuid: string): Promise<{
  allowed: boolean;
  status: RateLimitStatus;
  userId: number;
}> {
  console.log('🛡️ Enforcing atomic rate limit for UUID:', supabaseUuid);
  
  try {
    const supabase = createServiceClient();
    
    // Step 1: Get or create numeric user ID
    const { data: userIdData, error: userIdError } = await supabase.rpc('get_or_create_numeric_user_id', {
      input_uuid: supabaseUuid
    });

    if (userIdError) {
      throw new Error(`Failed to get user ID: ${userIdError.message}`);
    }

    const userId = userIdData;
    console.log('✅ Mapped to numeric ID:', userId);

    // Step 2: Atomic check and decrement operation
    const { data, error } = await supabase.rpc('atomic_rate_limit_check', {
      user_id_param: userId
    });

    if (error) {
      console.error('❌ Error in atomic rate limit check:', error);
      throw new Error(`Atomic rate limit check failed: ${error.message}`);
    }

    const status: RateLimitStatus = {
      allowed: data.allowed,
      userRemaining: data.user_remaining,
      userLimit: data.user_limit,
      globalRemaining: data.global_remaining,
      globalLimit: data.global_limit,
      resetTime: 'midnight UTC',
      reason: data.allowed ? undefined : (
        data.user_remaining <= 0 ? 'User daily limit exceeded' : 'Global daily limit exceeded'
      )
    };

    console.log('🛡️ Atomic rate limit result:', {
      allowed: status.allowed,
      reason: status.reason,
      userRemaining: status.userRemaining,
      globalRemaining: status.globalRemaining
    });
    
    return {
      allowed: status.allowed,
      status,
      userId
    };
  } catch (error) {
    console.error('❌ Critical error in enforceRateLimitAtomic:', error);
    
    // In case of database errors, we fail closed (deny the request)
    // This is safer than allowing unlimited requests
    return {
      allowed: false,
      status: {
        allowed: false,
        userRemaining: 0,
        userLimit: 0,
        globalRemaining: 0,
        globalLimit: 0,
        resetTime: 'midnight UTC',
        reason: 'Rate limiting system temporarily unavailable'
      },
      userId: 0
    };
  }
}

/**
 * Main rate limiting function that combines all checks
 * Returns true if generation should proceed, false otherwise
 * @deprecated Use enforceRateLimitAtomic for better concurrency safety
 */
export async function enforceRateLimit(supabaseUuid: string): Promise<{
  allowed: boolean;
  status: RateLimitStatus;
  userId: number;
}> {
  console.log('⚠️ Warning: Using legacy rate limit function. Consider upgrading to atomic version.');
  return enforceRateLimitAtomic(supabaseUuid);
}
