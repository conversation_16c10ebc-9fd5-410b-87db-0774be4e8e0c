/**
 * OAuth security utilities for redirect validation and CSRF protection
 */
import { clientEnv } from './envConfig';

// Allowed redirect URLs for OAuth callbacks
const ALLOWED_REDIRECT_URLS = [
  // Production URLs
  clientEnv.site.url,
  
  // Development URLs
  'http://localhost:3000',
  'http://127.0.0.1:3000',
  'https://localhost:3000',
  
  // Add any additional allowed domains here
  // 'https://your-production-domain.com',
  // 'https://your-staging-domain.com',
].filter(Boolean); // Remove undefined values

/**
 * Validates if a redirect URL is allowed
 */
export function isValidRedirectUrl(redirectUrl: string): boolean {
  try {
    const url = new URL(redirectUrl);
    
    // Check against allowed base URLs
    for (const allowedUrl of ALLOWED_REDIRECT_URLS) {
      if (!allowedUrl) continue;
      
      const allowed = new URL(allowedUrl);
      
      // Must match protocol, host, and port
      if (url.protocol === allowed.protocol && 
          url.host === allowed.host) {
        
        // Additional path validation - must start with allowed path
        const allowedPaths = ['/auth/callback', '/editor', '/'];
        const isPathAllowed = allowedPaths.some(path => 
          url.pathname === path || url.pathname.startsWith(path + '/')
        );
        
        if (isPathAllowed) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    // Invalid URL format
    console.error('Invalid redirect URL format:', redirectUrl, error);
    return false;
  }
}

/**
 * Sanitizes redirect URL to prevent open redirect attacks
 */
export function sanitizeRedirectUrl(redirectUrl: string): string {
  try {
    const url = new URL(redirectUrl);
    
    // Ensure protocol is HTTPS in production
    if (process.env.NODE_ENV === 'production' && url.protocol !== 'https:') {
      url.protocol = 'https:';
    }
    
    // Remove any dangerous query parameters
    const dangerousParams = ['javascript', 'data', 'vbscript', 'file'];
    for (const param of dangerousParams) {
      url.searchParams.delete(param);
    }
    
    // Limit query string length
    if (url.search.length > 500) {
      url.search = '';
    }
    
    return url.toString();
  } catch (error) {
    console.error('Error sanitizing redirect URL:', error);
    // Return safe default
    return clientEnv.site.url;
  }
}

/**
 * Generates a secure OAuth state parameter for CSRF protection
 */
export function generateOAuthState(): string {
  // Generate cryptographically secure random state
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  } else {
    // Fallback for older environments
    return Math.random().toString(36).substring(2) + 
           Math.random().toString(36).substring(2) + 
           Date.now().toString(36);
  }
}

/**
 * Validates OAuth state parameter to prevent CSRF attacks
 */
export function validateOAuthState(state: string, expectedState: string): boolean {
  if (!state || !expectedState) {
    return false;
  }
  
  // Use timing-safe comparison to prevent timing attacks
  if (state.length !== expectedState.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < state.length; i++) {
    result |= state.charCodeAt(i) ^ expectedState.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * Validates and sanitizes OAuth callback parameters
 */
export function validateOAuthCallback(params: {
  code?: string;
  state?: string;
  error?: string;
  error_description?: string;
}): {
  isValid: boolean;
  sanitizedParams: typeof params;
  errors: string[];
} {
  const errors: string[] = [];
  const sanitizedParams = { ...params };
  
  // Validate code parameter
  if (params.code) {
    // OAuth authorization codes should be alphanumeric with specific length
    if (!/^[a-zA-Z0-9_-]{10,512}$/.test(params.code)) {
      errors.push('Invalid authorization code format');
      delete sanitizedParams.code;
    }
  }
  
  // Validate state parameter
  if (params.state) {
    // State should be alphanumeric
    if (!/^[a-zA-Z0-9_-]{16,128}$/.test(params.state)) {
      errors.push('Invalid state parameter format');
      delete sanitizedParams.state;
    }
  }
  
  // Sanitize error parameters
  if (params.error) {
    // Limit error parameter length and characters
    sanitizedParams.error = params.error.substring(0, 100).replace(/[^a-zA-Z0-9_-]/g, '');
  }
  
  if (params.error_description) {
    // Sanitize error description
    sanitizedParams.error_description = params.error_description
      .substring(0, 500)
      .replace(/[<>'"]/g, ''); // Remove potential XSS characters
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedParams,
    errors
  };
}

/**
 * Gets the default safe redirect URL
 */
export function getDefaultRedirectUrl(): string {
  return process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
}

/**
 * Validates OAuth provider to prevent injection attacks
 */
export function isValidOAuthProvider(provider: string): boolean {
  const allowedProviders = ['google', 'twitter', 'github', 'facebook'];
  return allowedProviders.includes(provider.toLowerCase());
}

/**
 * Rate limiting for OAuth attempts to prevent abuse
 */
const oauthAttempts = new Map<string, { count: number; lastAttempt: number }>();

export function checkOAuthRateLimit(identifier: string, maxAttempts = 5, windowMs = 15 * 60 * 1000): boolean {
  const now = Date.now();
  const attempts = oauthAttempts.get(identifier);
  
  if (!attempts) {
    oauthAttempts.set(identifier, { count: 1, lastAttempt: now });
    return true;
  }
  
  // Reset if window has passed
  if (now - attempts.lastAttempt > windowMs) {
    oauthAttempts.set(identifier, { count: 1, lastAttempt: now });
    return true;
  }
  
  // Check if limit exceeded
  if (attempts.count >= maxAttempts) {
    return false;
  }
  
  // Increment counter
  attempts.count++;
  attempts.lastAttempt = now;
  oauthAttempts.set(identifier, attempts);
  
  return true;
}

/**
 * Cleans up old rate limit entries
 */
export function cleanupOAuthRateLimit(): void {
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  
  for (const [key, value] of oauthAttempts.entries()) {
    if (now - value.lastAttempt > windowMs) {
      oauthAttempts.delete(key);
    }
  }
}

// Run cleanup every 5 minutes if in server environment
if (typeof window === 'undefined') {
  setInterval(cleanupOAuthRateLimit, 5 * 60 * 1000);
}