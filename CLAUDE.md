# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
bun run build        # Build production application
bun run start        # Start production server
bun run lint         # Run ESLint
```

## Architecture Overview

This is a Next.js 15 application with Supabase authentication using the cookie-based SSR pattern. The app demonstrates a complete auth flow with protected routes.

### Key Technologies
- **Next.js 15** with App Router and React 19
- **Supabase** for authentication and database
- **TypeScript** in strict mode
- **Tailwind CSS** with shadcn/ui components
- **next-themes** for dark mode support

### Authentication Flow
- Uses Supabase SSR (`@supabase/ssr`) with cookie-based sessions
- Middleware handles session refresh and route protection
- Three Supabase clients: browser, server, and middleware
- Protected routes redirect to `/auth/login` if not authenticated

### Directory Structure
```
app/
├── auth/              # Authentication pages (login, signup, etc.)
├── protected/         # Protected routes requiring authentication
├── layout.tsx         # Root layout with ThemeProvider
├── page.tsx           # Landing page
└── globals.css        # Global styles

components/
├── auth-button.tsx    # Login/logout button
├── tutorial/          # Tutorial components
└── ui/                # shadcn/ui components

lib/
├── supabase/          # Supabase client configurations
│   ├── client.ts      # Browser client
│   ├── server.ts      # Server-side client
│   └── middleware.ts  # Middleware client
└── utils.ts           # Utility functions
```

### Configuration Files
- `components.json`: shadcn/ui configuration (New York style, CSS variables)
- `tailwind.config.ts`: Tailwind with shadcn/ui color system
- `next.config.ts`: Minimal Next.js config
- `eslint.config.mjs`: ESLint with Next.js and TypeScript rules

### Environment Variables Required
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your_supabase_anon_key
```

## Code Patterns

### Supabase Client Usage
- **Browser components**: Use `createClient()` from `@/lib/supabase/client`
- **Server components**: Use `createClient()` from `@/lib/supabase/server`
- **Middleware**: Use `updateSession()` from `@/lib/supabase/middleware`
- Always create new server clients per request (important for Fluid compute)

### Authentication Check Pattern
```typescript
const supabase = await createClient();
const { data, error } = await supabase.auth.getClaims();
if (error || !data?.claims) {
  redirect("/auth/login");
}
```

### Component Structure
- Uses React Server Components by default
- shadcn/ui components in `/components/ui/`
- Custom components follow TypeScript functional component pattern
- Proper use of `next/font` with Geist font

### Route Protection
- Middleware runs on all routes except static files and images
- Redirects unauthenticated users to `/auth/login`
- Protected routes are in `/app/protected/` directory

## Important Implementation Notes

### Supabase SSR Configuration
- Uses cookie-based session management for SSR compatibility
- Middleware handles session refresh automatically
- Environment variable `hasEnvVars` utility checks for required config

### UI System
- shadcn/ui with "new-york" style and CSS variables
- Dark mode support via `next-themes`
- Tailwind with custom color system and animations
- Lucide icons for UI elements

### Development Best Practices
- TypeScript strict mode enabled
- ESLint with Next.js and TypeScript rules
- Proper error handling in auth flows
- Server-side rendering with proper hydration

## Memories and Notes

- continue, and keep gpt-image-1 
- Dont read the svg, just use it