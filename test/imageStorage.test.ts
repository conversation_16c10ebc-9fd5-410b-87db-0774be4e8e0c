/**
 * Simple test file for image storage functionality
 * Run this in the browser console to test the storage system
 */

import { 
  storeProcessedImage, 
  getStoredImages, 
  clearAllImages,
  getStorageStats,
  isStorageAvailable 
} from '@/lib/imageStorage';

// Test data - small base64 image for testing
const TEST_IMAGE_BASE64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

export async function runImageStorageTests() {
  console.log('🧪 Starting Image Storage Tests...');
  
  try {
    // Test 1: Check storage availability
    console.log('\n1️⃣ Testing storage availability...');
    const available = await isStorageAvailable();
    console.log(`Storage available: ${available}`);
    
    if (!available) {
      console.error('❌ Storage not available, stopping tests');
      return;
    }

    // Test 2: Clear existing data
    console.log('\n2️⃣ Clearing existing data...');
    await clearAllImages();
    console.log('✅ Data cleared');

    // Test 3: Store test images
    console.log('\n3️⃣ Storing test images...');
    
    for (let i = 1; i <= 7; i++) {
      await storeProcessedImage(
        TEST_IMAGE_BASE64,
        TEST_IMAGE_BASE64,
        `test-image-${i}.png`,
        `Test prompt ${i}`,
        'Test Model'
      );
      console.log(`✅ Stored image ${i}`);
      
      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // Test 4: Check that only 5 images are kept (LRU cleanup)
    console.log('\n4️⃣ Checking LRU cleanup...');
    const images = await getStoredImages();
    console.log(`Images stored: ${images.length} (should be 5)`);
    
    if (images.length === 5) {
      console.log('✅ LRU cleanup working correctly');
      
      // Check that the latest 5 images are kept (3, 4, 5, 6, 7)
      const expectedNames = ['test-image-7.png', 'test-image-6.png', 'test-image-5.png', 'test-image-4.png', 'test-image-3.png'];
      const actualNames = images.map(img => img.originalFileName);
      
      console.log('Expected:', expectedNames);
      console.log('Actual:', actualNames);
      
      const isCorrectOrder = expectedNames.every((name, index) => actualNames[index] === name);
      if (isCorrectOrder) {
        console.log('✅ Correct images kept in correct order');
      } else {
        console.log('❌ Incorrect images or order');
      }
    } else {
      console.log('❌ LRU cleanup not working correctly');
    }

    // Test 5: Check storage stats
    console.log('\n5️⃣ Checking storage stats...');
    const stats = await getStorageStats();
    console.log('Storage stats:', stats);
    
    if (stats.count === 5 && stats.totalSize > 0) {
      console.log('✅ Storage stats working correctly');
    } else {
      console.log('❌ Storage stats incorrect');
    }

    // Test 6: Test image retrieval
    console.log('\n6️⃣ Testing image retrieval...');
    if (images.length > 0) {
      const firstImage = images[0];
      console.log('First image:', {
        id: firstImage.id,
        fileName: firstImage.originalFileName,
        processedAt: firstImage.processedAt,
        hasProcessedUrl: !!firstImage.processedImageUrl,
        hasThumbnail: !!firstImage.thumbnail
      });
      console.log('✅ Image retrieval working');
    }

    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Export for manual testing
if (typeof window !== 'undefined') {
  (window as any).runImageStorageTests = runImageStorageTests;
  console.log('💡 Run window.runImageStorageTests() in the browser console to test storage');
}
