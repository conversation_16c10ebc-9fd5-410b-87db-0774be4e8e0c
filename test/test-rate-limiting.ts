#!/usr/bin/env bun
/**
 * Test script for the rate limiting system
 * Tests all cry_ tables and functions end-to-end
 */

import { createClient } from '@supabase/supabase-js';
import { 
  enforceRateLimit, 
  decrementLimits, 
  logGenerationAttempt,
  getUserNumericId,
  checkGenerationAllowed 
} from '../lib/rateLimiter';

// Test configuration
const TEST_UUID = '123e4567-e89b-12d3-a456-************';
const TEST_PROMPT = 'Test prompt for rate limiting verification';

async function testRateLimitingSystem() {
  console.log('🧪 Starting Rate Limiting System Test\n');

  try {
    // Test 1: UUID to Numeric ID Mapping
    console.log('📋 Test 1: UUID to Numeric ID Mapping');
    console.log('🔄 Testing getUserNumericId...');
    const numericId = await getUserNumericId(TEST_UUID);
    console.log(`✅ UUID ${TEST_UUID} mapped to numeric ID: ${numericId}\n`);

    // Test 2: Check Generation Allowed
    console.log('📋 Test 2: Check Generation Allowed');
    console.log('🔍 Testing checkGenerationAllowed...');
    const status = await checkGenerationAllowed(numericId);
    console.log('✅ Rate limit status:', {
      allowed: status.allowed,
      userRemaining: status.userRemaining,
      userLimit: status.userLimit,
      globalRemaining: status.globalRemaining,
      globalLimit: status.globalLimit
    });
    console.log('');

    // Test 3: Full Rate Limit Enforcement
    console.log('📋 Test 3: Full Rate Limit Enforcement');
    console.log('🛡️ Testing enforceRateLimit...');
    const enforcement = await enforceRateLimit(TEST_UUID);
    console.log('✅ Rate limit enforcement result:', {
      allowed: enforcement.allowed,
      userId: enforcement.userId,
      reason: enforcement.status.reason
    });
    console.log('');

    // Test 4: Log Generation Attempt (Success)
    console.log('📋 Test 4: Log Generation Attempt (Success)');
    console.log('📝 Testing logGenerationAttempt (success)...');
    await logGenerationAttempt({
      userId: numericId,
      prompt: TEST_PROMPT,
      success: true,
      imageUrl: 'https://example.com/test-image.jpg'
    });
    console.log('✅ Successfully logged generation attempt\n');

    // Test 5: Log Generation Attempt (Failure)
    console.log('📋 Test 5: Log Generation Attempt (Failure)');
    console.log('📝 Testing logGenerationAttempt (failure)...');
    await logGenerationAttempt({
      userId: numericId,
      prompt: TEST_PROMPT,
      success: false,
      errorMessage: 'Test error for rate limiting verification'
    });
    console.log('✅ Successfully logged failed generation attempt\n');

    // Test 6: Decrement Limits (if allowed)
    if (enforcement.allowed) {
      console.log('📋 Test 6: Decrement Limits');
      console.log('⬇️ Testing decrementLimits...');
      const decrementSuccess = await decrementLimits(numericId);
      console.log(`✅ Decrement limits result: ${decrementSuccess}\n`);

      // Test 7: Check Status After Decrement
      console.log('📋 Test 7: Check Status After Decrement');
      console.log('🔍 Checking status after decrement...');
      const statusAfter = await checkGenerationAllowed(numericId);
      console.log('✅ Status after decrement:', {
        allowed: statusAfter.allowed,
        userRemaining: statusAfter.userRemaining,
        globalRemaining: statusAfter.globalRemaining
      });
      console.log('');
    } else {
      console.log('⚠️ Skipping decrement test - rate limit already exceeded\n');
    }

    // Test 8: Database Direct Verification
    console.log('📋 Test 8: Database Direct Verification');
    console.log('🔍 Verifying data in cry_ tables...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Check user mapping exists
    const { data: mapping } = await supabase
      .from('cry_user_id_mappings')
      .select('*')
      .eq('supabase_uuid', TEST_UUID)
      .single();
    
    console.log('✅ User mapping found:', mapping ? 'Yes' : 'No');

    // Check user limits exist
    const { data: userLimits } = await supabase
      .from('cry_user_limits')
      .select('*')
      .eq('user_id', numericId)
      .single();
    
    console.log('✅ User limits found:', userLimits ? 'Yes' : 'No');

    // Check generation logs exist
    const { data: logs } = await supabase
      .from('cry_generation_logs')
      .select('count')
      .eq('user_id', numericId);
    
    console.log('✅ Generation logs found:', logs ? logs.length : 0, 'entries');

    console.log('\n🎉 All Rate Limiting Tests Completed Successfully!');
    console.log('\n📊 Summary:');
    console.log('✅ cry_user_id_mappings: Working');
    console.log('✅ cry_user_limits: Working');
    console.log('✅ cry_generation_logs: Working');
    console.log('✅ cry_global_settings: Working');
    console.log('✅ All RPC functions: Working');
    console.log('✅ Rate limiting enforcement: Working');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testRateLimitingSystem();
}

export { testRateLimitingSystem };
