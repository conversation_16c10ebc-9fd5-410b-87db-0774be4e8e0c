import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import Image from "next/image";

export default async function Home() {
  // Check if user is already authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // If authenticated, redirect to editor
  if (user) {
    redirect("/editor");
  }

  return (
    <main className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 fade-in">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center mb-4">
            <div className="w-24 h-24 relative group">
              <Image
                src="/logo-transparent.svg"
                alt="Tears of the Left Logo"
                width={96}
                height={96}
                className="w-full h-full object-contain animate-pulse-slow"
              />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-foreground">
            Tears of the Left
          </h1>
          <p className="text-secondary-foreground">
            Transform your images with AI
          </p>
        </div>

        {/* Auth Forms */}
        <div className="space-y-6">
          <AuthTabs />
        </div>


      </div>
    </main>
  );
}

function AuthTabs() {
  return (
    <div className="space-y-4">
      <Card className="border-accent/30 bg-card/90 shadow-xl backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-center text-xl font-bold text-foreground">Welcome</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <Button asChild variant="outline" className="h-12 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent text-foreground font-semibold transition-all duration-200 hover:scale-105">
              <a href="/auth/login">Sign In</a>
            </Button>
            <Button asChild className="h-12 bg-primary hover:bg-primary/80 text-primary-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 transform">
              <a href="/auth/sign-up">Sign Up</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
