import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";
import { type NextRequest } from "next/server";
import { 
  isValidRedirectUrl, 
  sanitizeRedirectUrl, 
  validateOAuthCallback,
  checkOAuthRateLimit,
  getDefaultRedirectUrl 
} from "@/lib/oauthSecurity";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const clientIP = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown';

  console.log('🔐 OAuth callback received from IP:', clientIP);

  // Rate limiting for OAuth callbacks
  if (!checkOAuthRateLimit(clientIP, 10, 5 * 60 * 1000)) { // 10 attempts per 5 minutes
    console.error('❌ OAuth rate limit exceeded for IP:', clientIP);
    return NextResponse.redirect(
      `${getDefaultRedirectUrl()}/auth/error?error=${encodeURIComponent(
        "Too many authentication attempts. Please try again later."
      )}`
    );
  }

  // Extract and validate OAuth parameters
  const oauthParams = {
    code: requestUrl.searchParams.get("code") || undefined,
    state: requestUrl.searchParams.get("state") || undefined,
    error: requestUrl.searchParams.get("error") || undefined,
    error_description: requestUrl.searchParams.get("error_description") || undefined
  };

  // Validate OAuth callback parameters
  const validation = validateOAuthCallback(oauthParams);
  
  if (!validation.isValid) {
    console.error("❌ Invalid OAuth callback parameters:", validation.errors);
    return NextResponse.redirect(
      `${getDefaultRedirectUrl()}/auth/error?error=${encodeURIComponent(
        "Invalid authentication parameters"
      )}`
    );
  }

  const { code, error, error_description } = validation.sanitizedParams;

  // Handle OAuth errors
  if (error || error_description) {
    console.error("OAuth callback error:", { error, error_description });
    return NextResponse.redirect(
      `${getDefaultRedirectUrl()}/auth/error?error=${encodeURIComponent(
        error_description || error || "Authentication failed"
      )}`
    );
  }

  if (code) {
    const supabase = await createClient();
    
    try {
      // Exchange the code for a session
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);
      
      if (exchangeError) {
        console.error("Code exchange error:", exchangeError);
        return NextResponse.redirect(
          `${requestUrl.origin}/auth/error?error=${encodeURIComponent(
            exchangeError.message
          )}`
        );
      }

      if (data?.session) {
        // Validate and sanitize redirect URL
        const redirectUrl = `${requestUrl.origin}/editor`;
        
        if (isValidRedirectUrl(redirectUrl)) {
          const safeRedirectUrl = sanitizeRedirectUrl(redirectUrl);
          console.log('✅ OAuth authentication successful, redirecting to:', safeRedirectUrl);
          return NextResponse.redirect(safeRedirectUrl);
        } else {
          console.error('❌ Invalid redirect URL detected:', redirectUrl);
          return NextResponse.redirect(getDefaultRedirectUrl());
        }
      }
    } catch (err) {
      console.error("Unexpected error during OAuth callback:", err);
      return NextResponse.redirect(
        `${getDefaultRedirectUrl()}/auth/error?error=${encodeURIComponent(
          "Authentication failed. Please try again."
        )}`
      );
    }
  }

  // No code parameter found
  console.error("❌ No authorization code in OAuth callback");
  return NextResponse.redirect(
    `${getDefaultRedirectUrl()}/auth/error?error=${encodeURIComponent(
      "Invalid authentication request"
    )}`
  );
}