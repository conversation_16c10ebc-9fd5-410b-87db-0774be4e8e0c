import { createClient } from "@/lib/supabase/server";
import { type EmailOtpType } from "@supabase/supabase-js";
import { redirect } from "next/navigation";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get("token_hash");
  const type = searchParams.get("type") as EmailOtpType | null;
  const next = searchParams.get("next") ?? "/editor";
  const code = searchParams.get("code");
  const error_description = searchParams.get("error_description");

  // Handle OAuth errors
  if (error_description) {
    console.error("OAuth error:", error_description);
    redirect(`/auth/error?error=${encodeURIComponent(error_description)}`);
  }

  // Handle OAuth callback with code
  if (code) {
    const supabase = await createClient();
    
    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error("Code exchange error:", error);
        redirect(`/auth/error?error=${encodeURIComponent(error.message)}`);
      }
      
      if (data?.session) {
        // OAuth login successful, redirect to editor
        redirect("/editor");
      }
    } catch (err) {
      console.error("Unexpected error during code exchange:", err);
      redirect(`/auth/error?error=${encodeURIComponent('Authentication failed. Please try again.')}`);
    }
  }

  // Handle email confirmation with OTP
  if (token_hash && type) {
    const supabase = await createClient();

    try {
      const { error } = await supabase.auth.verifyOtp({
        type,
        token_hash,
      });
      
      if (!error) {
        // Email verification successful
        redirect(next);
      } else {
        console.error("OTP verification error:", error);
        redirect(`/auth/error?error=${encodeURIComponent(error.message)}`);
      }
    } catch (err) {
      console.error("Unexpected error during OTP verification:", err);
      redirect(`/auth/error?error=${encodeURIComponent('Verification failed. Please try again.')}`);
    }
  }

  // If we get here, it's an invalid request
  console.error("Invalid auth request - missing required parameters");
  redirect(`/auth/error?error=${encodeURIComponent('Invalid authentication request')}`);
}
