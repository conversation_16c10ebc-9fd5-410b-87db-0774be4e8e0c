@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Pure black, gold, and white theme */
    --background: 0 0% 0%;              /* #000000 - Pure black background */
    --foreground: 0 0% 100%;            /* #ffffff - Pure white text */
    --card: 0 0% 0%;                    /* #000000 - Pure black card background */
    --card-foreground: 0 0% 100%;       /* #ffffff - Pure white text */
    --popover: 0 0% 5%;                 /* #0d0d0d - Slightly lighter black for popovers */
    --popover-foreground: 0 0% 100%;    /* #ffffff - Pure white text */
    --primary: 51 100% 50%;             /* #ffd700 - Gold primary color */
    --primary-foreground: 0 0% 0%;      /* #000000 - Black text on gold */
    --secondary: 45 100% 35%;           /* #b8860b - Darker gold secondary */
    --secondary-foreground: 0 0% 100%;  /* #ffffff - White text */
    --muted: 0 0% 15%;                  /* #262626 - Dark gray muted */
    --muted-foreground: 0 0% 80%;       /* #cccccc - Light gray text */
    --accent: 51 100% 50%;              /* #ffd700 - Gold accent */
    --accent-foreground: 0 0% 0%;       /* #000000 - Black text on gold */
    --destructive: 0 80% 65%;           /* #e16162 - Red for destructive */
    --destructive-foreground: 0 0% 100%; /* #ffffff - White text */
    --border: 51 100% 50%;              /* #ffd700 - Gold border */
    --input: 0 0% 5%;                   /* #0d0d0d - Dark input background */
    --ring: 51 100% 50%;                /* #ffd700 - Gold ring */
    --chart-1: 51 100% 50%;             /* #ffd700 - Gold chart color */
    --chart-2: 45 100% 35%;             /* #b8860b - Darker gold chart color */
    --chart-3: 0 80% 65%;               /* #e16162 - Red chart color */
    --chart-4: 0 0% 80%;                /* #cccccc - Light gray chart color */
    --chart-5: 0 0% 0%;                 /* #000000 - Black chart color */
    --radius: 0.5rem;
  }
  .dark {
    /* Same colors for dark mode - pure black, gold, white theme */
    --background: 0 0% 0%;              /* #000000 - Pure black background */
    --foreground: 0 0% 100%;            /* #ffffff - Pure white text */
    --card: 0 0% 0%;                    /* #000000 - Pure black card background */
    --card-foreground: 0 0% 100%;       /* #ffffff - Pure white text */
    --popover: 0 0% 5%;                 /* #0d0d0d - Slightly lighter black for popovers */
    --popover-foreground: 0 0% 100%;    /* #ffffff - Pure white text */
    --primary: 51 100% 50%;             /* #ffd700 - Gold primary color */
    --primary-foreground: 0 0% 0%;      /* #000000 - Black text on gold */
    --secondary: 45 100% 35%;           /* #b8860b - Darker gold secondary */
    --secondary-foreground: 0 0% 100%;  /* #ffffff - White text */
    --muted: 0 0% 15%;                  /* #262626 - Dark gray muted */
    --muted-foreground: 0 0% 80%;       /* #cccccc - Light gray text */
    --accent: 51 100% 50%;              /* #ffd700 - Gold accent */
    --accent-foreground: 0 0% 0%;       /* #000000 - Black text on gold */
    --destructive: 0 80% 65%;           /* #e16162 - Red for destructive */
    --destructive-foreground: 0 0% 100%; /* #ffffff - White text */
    --border: 51 100% 50%;              /* #ffd700 - Gold border */
    --input: 0 0% 5%;                   /* #0d0d0d - Dark input background */
    --ring: 51 100% 50%;                /* #ffd700 - Gold ring */
    --chart-1: 51 100% 50%;             /* #ffd700 - Gold chart color */
    --chart-2: 45 100% 35%;             /* #b8860b - Darker gold chart color */
    --chart-3: 0 80% 65%;               /* #e16162 - Red chart color */
    --chart-4: 0 0% 80%;                /* #cccccc - Light gray chart color */
    --chart-5: 0 0% 0%;                 /* #000000 - Black chart color */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html, body {
    @apply bg-background text-foreground;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
  
  #__next {
    width: 100%;
    min-height: 100vh;
  }
}

@layer utilities {
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  @keyframes pulse-slow {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
  
  .animate-spin-slow {
    animation: spin-slow 8s linear infinite;
  }
  
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .animate-star-clockwise {
    animation: star-clockwise 4s ease-in-out infinite;
  }
  
  @keyframes star-clockwise {
    0% {
      opacity: 0;
      stroke-dasharray: 0 1000;
      transform: scale(0.3) rotate(-90deg);
    }
    25% {
      opacity: 1;
      stroke-dasharray: 500 1000;
      transform: scale(1.1) rotate(0deg);
    }
    50% {
      opacity: 1;
      stroke-dasharray: 1000 1000;
      transform: scale(1) rotate(90deg);
    }
    75% {
      opacity: 0.8;
      stroke-dasharray: 1000 1000;
      transform: scale(0.9) rotate(180deg);
    }
    100% {
      opacity: 0;
      stroke-dasharray: 0 1000;
      transform: scale(0.3) rotate(270deg);
    }
  }

  /* Loading Overlay Animations - Sipario Theater Curtain Effect */
  .animate-sipario-left {
    animation: siparioLeft 0.35s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-sipario-right {
    animation: siparioRight 0.35s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  @keyframes siparioLeft {
    0% { transform: translateX(0); }
    100% { transform: translateX(-101%); }
  }

  @keyframes siparioRight {
    0% { transform: translateX(0); }
    100% { transform: translateX(101%); }
  }

  /* Curtain fold animations */
  .curtain-fold {
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .animate-sipario-left .curtain-fold,
  .animate-sipario-right .curtain-fold {
    animation: foldGather 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  @keyframes foldGather {
    0% {
      opacity: 0.7;
      transform: scaleX(1);
    }
    50% {
      opacity: 0.9;
      transform: scaleX(1.2);
    }
    100% {
      opacity: 1;
      transform: scaleX(2);
    }
  }

  /* Teardrop split like a lock */
  .tear-left {
    transform-origin: 1107.5px 703px;
    animation: tearLeft 0.28s cubic-bezier(0.2, 0.7, 0.2, 1) forwards;
  }
  .tear-right {
    transform-origin: 1107.5px 703px;
    animation: tearRight 0.28s cubic-bezier(0.2, 0.7, 0.2, 1) forwards;
  }

  @keyframes tearLeft {
    0% { transform: translateX(0) translateY(0) rotate(0deg); opacity: 1; }
    60% { transform: translateX(-8px) translateY(-2px) rotate(-3deg); opacity: 1; }
    100% { transform: translateX(-20px) translateY(-3px) rotate(-5deg); opacity: 0; }
  }
  @keyframes tearRight {
    0% { transform: translateX(0) translateY(0) rotate(0deg); opacity: 1; }
    60% { transform: translateX(8px) translateY(-2px) rotate(3deg); opacity: 1; }
    100% { transform: translateX(20px) translateY(-3px) rotate(5deg); opacity: 0; }
  }

  .animate-pulse-glow {
    animation: pulseGlow 1.5s ease-in-out infinite;
  }

  @keyframes pulseGlow {
    0%, 100% {
      filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
      transform: scale(1);
    }
    50% {
      filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.8));
      transform: scale(1.05);
    }
  }

  .tear-fall {
    animation: tearFall 0.32s cubic-bezier(0.22, 1, 0.36, 1) forwards;
    will-change: transform;
  }
  @keyframes tearFall {
    0% { transform: translateY(-60vh) scale(0.98); opacity: 0.9; }
    60% { transform: translateY(-12vh) scale(1); opacity: 1; }
    85% { transform: translateY(-2vh) scale(1.005); opacity: 1; }
    100% { transform: translateY(0) scale(1); opacity: 1; }
  }

  /* Text animations */
  .animate-shimmer {
    background: linear-gradient(
      105deg,
      #ffd700 0%,
      #ffed4e 25%,
      #ffd700 50%,
      #ffed4e 75%,
      #ffd700 100%
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s linear infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-fade-in {
    animation: fadeInUp 1s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-slide-left,
    .animate-slide-right,
    .animate-tear-fade,
    .animate-pulse-glow,
    .animate-shimmer,
    .animate-fade-in {
      animation: none !important;
    }
  }
}
