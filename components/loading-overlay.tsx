"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface LoadingOverlayProps {
  isLoading: boolean;
  onLoadingComplete?: () => void;
}

export function LoadingOverlay({ isLoading, onLoadingComplete }: LoadingOverlayProps) {
  const [showOverlay, setShowOverlay] = useState(isLoading);
  const [startAnimation, setStartAnimation] = useState(false);

  useEffect(() => {
    if (isLoading) {
      setShowOverlay(true);
      setStartAnimation(false); // Reset animation state
      // Start the curtain split after tear turns gold
       setTimeout(() => {
          console.log('🎭 Starting curtain animation');
          setStartAnimation(true);
         }, 120);      return () => {}    } else {
       // Start hiding animation immediately when isLoading becomes false
       console.log('🎭 Starting hide animation');
        setTimeout(() => {
         setShowOverlay(false);
         onLoadingComplete?.();
        }, 250);      return () => {}    }
  }, [isLoading, onLoadingComplete]);

  return (
    <AnimatePresence>
      {showOverlay && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: isLoading ? 1 : 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.12, delay: isLoading ? 0 : 0 }}
          className="fixed inset-0 z-[9999] pointer-events-none"
          style={{ background: 'transparent' }}
        >
          {/* Background that will be revealed */}
          <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-background via-background/95 to-background/90" />
          
          {/* SVG Curtain Animation */}
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 2215 1407"
            preserveAspectRatio="xMidYMid slice"
            className="absolute inset-0 w-full h-full"
          >
             <defs>
               <linearGradient id="tearGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                 <stop offset="0%" stopColor="#1a1a1a">
                   <animate attributeName="stop-color" values="#1a1a1a;#f9bc60;#ffd700" dur="1.0s" fill="freeze" />
                 </stop>
                 <stop offset="100%" stopColor="#000000">
                   <animate attributeName="stop-color" values="#000000;#e6a050;#ffcc00" dur="1.0s" fill="freeze" />
                 </stop>
               </linearGradient>
               <filter id="tearGlow">
                 <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
                 <feMerge>
                   <feMergeNode in="coloredBlur"/>
                   <feMergeNode in="SourceGraphic"/>
                 </feMerge>
               </filter>
             </defs>
            {/* Full-screen panels for visible split */}
            <g>
              <g className={`${startAnimation ? 'animate-sipario-left' : ''}`} data-animation={startAnimation ? 'active' : 'inactive'}>
                <rect x="0" y="0" width="1108" height="1407" fill="black" />
                <rect x="0" y="0" width="50" height="1407" fill="rgba(0,0,0,0.3)" className="curtain-fold" />
                <rect x="200" y="0" width="30" height="1407" fill="rgba(0,0,0,0.2)" className="curtain-fold" />
                <rect x="500" y="0" width="40" height="1407" fill="rgba(0,0,0,0.25)" className="curtain-fold" />
                <rect x="800" y="0" width="35" height="1407" fill="rgba(0,0,0,0.2)" className="curtain-fold" />
              </g>
              <g className={`${startAnimation ? 'animate-sipario-right' : ''}`} data-animation={startAnimation ? 'active' : 'inactive'}>
                <rect x="1107" y="0" width="1108" height="1407" fill="black" />
                <rect x="2165" y="0" width="50" height="1407" fill="rgba(0,0,0,0.3)" className="curtain-fold" />
                <rect x="1985" y="0" width="30" height="1407" fill="rgba(0,0,0,0.2)" className="curtain-fold" />
                <rect x="1670" y="0" width="40" height="1407" fill="rgba(0,0,0,0.25)" className="curtain-fold" />
                <rect x="1380" y="0" width="35" height="1407" fill="rgba(0,0,0,0.2)" className="curtain-fold" />
              </g>
            </g>

              {/* Tear falls from top to center while curtains split */}
              <g>
                <g className={`${startAnimation ? 'tear-fall' : ''}`}>
                  <path
                    d="M1107.5 658C1103.72 695.615 1080 695.443 1080 721.378C1080 734.603 1091.52 748 1107.5 748C1123.48 748 1135 734.088 1135 721.378C1135 695.443 1110.59 695.443 1107.5 658Z"
                    fill="url(#tearGradient)"
                    filter="url(#tearGlow)"
                  />
                </g>
              </g>          </svg>
        </motion.div>
      )}
    </AnimatePresence>
  );
}