"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { LoadingOverlay } from './loading-overlay';
import { usePathname } from 'next/navigation';

interface LoadingContextType {
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  showLoading: () => void;
  hideLoading: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

interface LoadingProviderProps {
  children: React.ReactNode;
  initialLoading?: boolean;
}

export function LoadingProvider({ children, initialLoading = true }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(initialLoading);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const pathname = usePathname();

  // Show loading animation on initial page load
  useEffect(() => {
    if (isInitialLoad) {
      // Keep loading true for the full animation duration
      const timer = setTimeout(() => {
        setIsLoading(false);
        setIsInitialLoad(false);
      }, 4500); // Wait for full animation to complete

      return () => clearTimeout(timer);
    }
  }, [isInitialLoad]);

  // Optional: Show loading on route changes
  useEffect(() => {
    if (!isInitialLoad) {
      // You can enable this if you want loading animation on route changes
      // setIsLoading(true);
      // setTimeout(() => setIsLoading(false), 800);
    }
  }, [pathname, isInitialLoad]);

  const showLoading = useCallback(() => {
    setIsLoading(true);
  }, []);

  const hideLoading = useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleLoadingComplete = useCallback(() => {
    // Optional callback when loading animation completes
    console.log('Loading animation completed');
  }, []);

  return (
    <LoadingContext.Provider value={{ isLoading, setIsLoading, showLoading, hideLoading }}>
      <LoadingOverlay 
        isLoading={isLoading} 
        onLoadingComplete={handleLoadingComplete}
      />
      {children}
    </LoadingContext.Provider>
  );
}