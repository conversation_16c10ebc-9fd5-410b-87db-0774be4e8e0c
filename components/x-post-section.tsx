"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Twitter, Send, Copy, Sparkles } from "lucide-react";
import { toast } from "sonner";
import {
  shareToX,
  createDefaultPostText,
  ShareProgressCallback
} from "@/lib/x-utils";
import { Progress } from "@/components/ui/progress";

interface XPostSectionProps {
  imageUrl: string;
  className?: string;
}

export function XPostSection({ imageUrl, className }: XPostSectionProps) {
  const [postText, setPostText] = useState("");
  const [isSharing, setIsSharing] = useState(false);
  const [shareProgress, setShareProgress] = useState(0);
  const [shareStep, setShareStep] = useState('');

  // AI text transformation state
  const [isTransforming, setIsTransforming] = useState(false);
  const [transformError, setTransformError] = useState<string | null>(null);

  // Initialize with default post text
  useEffect(() => {
    setPostText(createDefaultPostText());
  }, []);

  const handleShare = async () => {
    console.log('❌ XPostSection: Starting share process');

    if (!postText.trim()) {
      toast.error("Please enter some text for your post.");
      return;
    }

    setIsSharing(true);
    
    try {
      const onProgress: ShareProgressCallback = (step, progress) => {
        setShareStep(step);
        setShareProgress(progress);
      };

      const result = await shareToX(
        imageUrl,
        postText,
        `tears-of-the-left-${Date.now()}.png`,
        onProgress
      );
      
      if (result.success) {
        toast.success(result.message, {
          duration: 5000,
          action: result.imageCopied ? {
            label: "Got it!",
            onClick: () => toast.dismiss()
          } : undefined
        });
      } else {
        toast.error(result.message, {
          duration: 7000,
          action: {
            label: "Retry",
            onClick: () => handleShare()
          }
        });
      }
    } catch (error) {
      console.error('❌ XPostSection: Share failed:', error);
      toast.error("Failed to share to X. Please try again.");
    } finally {
      setIsSharing(false);
      setShareProgress(0);
      setShareStep('');
    }
  };

  const handleTransformText = async () => {
    console.log('🤖 XPostSection: Starting AI text transformation');

    if (!postText.trim()) {
      toast.error("Please enter some text to transform.");
      return;
    }

    if (postText.length > 4000) {
      toast.error("Text too long for AI processing. Maximum 4000 characters.");
      return;
    }

    setIsTransforming(true);
    setTransformError(null);

    try {
      const response = await fetch('/api/text-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: postText.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Text transformation failed');
      }

      const data = await response.json();

      // Update the post text with the AI-enhanced version
      setPostText(data.processedText);

      toast.success("✨ Text transformed with AI!", {
        duration: 4000,
        description: "Your post text has been enhanced with emotional depth and expression."
      });

      console.log('🎉 XPostSection: Text transformation completed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Text transformation failed';
      setTransformError(errorMessage);
      toast.error(`Failed to transform text: ${errorMessage}`);
      console.error('❌ XPostSection: Text transformation failed:', err);
    } finally {
      setIsTransforming(false);
    }
  };

  const handleCopyText = async () => {
    try {
      await navigator.clipboard.writeText(postText);
      toast.success("Post text copied to clipboard!");
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error("Failed to copy text to clipboard.");
    }
  };

  return (
    <Card className={`border-accent/30 bg-secondary/5 shadow-xl ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Twitter className="h-5 w-5 text-accent" />
          Share to X
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Tweet Text Input */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor="post-text" className="text-sm font-medium text-foreground">
              Post Text
            </label>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyText}
              className="h-6 px-2 text-xs"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
          </div>
          
          <Textarea
            id="post-text"
            value={postText}
            onChange={(e) => setPostText(e.target.value)}
            placeholder="What's happening?"
            className="min-h-[100px] resize-none"
            maxLength={300} // Allow a bit over 280 for better UX
          />

          {/* Character count and AI transform button */}
          <div className="flex items-center justify-between text-sm">
            <div className="text-foreground/60">
              {postText.length}/280 characters
              {postText.length > 280 && (
                <span className="text-yellow-600 ml-2">Over X limit</span>
              )}
              {postText.length > 4000 && (
                <span className="text-red-600 ml-2">Too long for AI</span>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleTransformText}
              disabled={isTransforming || !postText.trim() || postText.length > 4000}
              className="h-8 px-3 text-xs border-accent/30 bg-background/50 hover:bg-yellow-400 hover:border-yellow-500 hover:text-black hover:shadow-lg hover:shadow-yellow-400/25 transform hover:scale-105 transition-all duration-300 ease-out group relative overflow-hidden"
            >
              {isTransforming ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Transforming...
                </>
              ) : (
                <>
                  <Sparkles className="h-3 w-3 mr-1 transition-all duration-300 group-hover:rotate-12 group-hover:scale-110 group-hover:drop-shadow-sm" />
                  Transform with AI
                </>
              )}
            </Button>
          </div>

          {/* Transform Error Display */}
          {transformError && (
            <div className="text-xs text-red-600 bg-red-50 dark:bg-red-950/20 p-2 rounded border border-red-200 dark:border-red-800">
              ⚠️ {transformError}
            </div>
          )}
        </div>



        {/* Instructions */}
        <div className="text-xs text-white p-3 bg-accent/5 rounded-lg border border-accent/20">
          <p className="font-medium mb-1 text-white">How it works:</p>
          <ol className="list-decimal list-inside space-y-1 text-white">
            <li>Your image will be copied to clipboard (if supported)</li>
            <li>Image will be downloaded as backup</li>
            <li className="font-medium text-accent">📱 On mobile: X app opens directly with your text</li>
            <li className="font-medium text-accent">💻 On desktop: X web opens in new tab</li>
            <li>Paste or upload the image in your post</li>
          </ol>
        </div>

        {/* Share Button */}
        {/* Share Progress */}
        {isSharing && (
          <div className="space-y-3 mb-4 p-4 bg-accent/5 rounded-lg border border-accent/20">
            <div className="text-sm font-medium text-foreground">
              {shareStep}
            </div>
            <Progress value={shareProgress} className="h-2" />
            <div className="text-xs text-foreground/70 text-right">
              {shareProgress}% complete
            </div>
          </div>
        )}

        <Button
          onClick={handleShare}
          disabled={isSharing || !postText.trim()}
          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:hover:scale-100 h-14"
        >
          {isSharing ? (
            <>
              <Loader2 className="h-5 w-5 mr-3 animate-spin" />
              Sharing to X... ({shareProgress}%)
            </>
          ) : (
            <>
              <Send className="h-5 w-5 mr-3" />
              Share to X
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
