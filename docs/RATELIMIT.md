# CryBaby Rate Limiting & Database Documentation

## 📊 Overview

CryBaby implements a **dual-layer hybrid rate limiting system** that protects both infrastructure and ensures fair usage across all users. The system uses Supabase (PostgreSQL) for persistent storage and atomic operations.

## 🛡️ Rate Limiting Architecture

### Dual-Layer System Design

The bot uses **two independent but coordinated rate limits**:

1. **Global Limit** (System-wide)
   - Protects infrastructure from overload
   - Shared across all users
   - Default: 100 generations/day
   - Admin configurable via admin panel
   - Prevents system abuse and OpenAI quota exhaustion

2. **User Limit** (Individual)
   - Ensures fair distribution among users
   - Per-user daily quota
   - Default: 10 generations/day
   - Prevents individual user monopolization
   - Admin configurable per user

### Rate Limiting Logic

**Both limits must have available capacity for generation to proceed.**

```
Generation Request
        ↓
Check User Limit > 0?
        ↓
Check Global Limit > 0?
        ↓
Both have capacity?
   ↙        ↘
  No         Yes
  ↓           ↓
Block      Generate
Request    & Decrement
           Both Limits
```

### Key Features

- **Atomic Operations**: Both limits are checked and decremented atomically to prevent race conditions
- **Auto-Reset**: Limits automatically reset daily at midnight UTC
- **Fail-Safe**: If either limit is exceeded, generation is blocked
- **Transparent**: Users see both their personal and global remaining capacity
- **Configurable**: Admins can adjust limits via the admin panel

## 🗄️ Supabase Database Schema

### Tables

#### 1. `cry_global_settings`
**Purpose**: System-wide rate limiting configuration

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| `id` | INTEGER | 1 | Primary key (always 1, single row constraint) |
| `daily_limit` | INTEGER | 100 | Maximum global generations per day |
| `remaining` | INTEGER | 100 | Remaining global generations today |
| `last_reset` | DATE | CURRENT_DATE | Date of last reset |

**Constraints**:
- Single row constraint: `id = 1`
- Only one global settings record can exist

#### 2. `cry_user_limits`
**Purpose**: Individual user rate limiting

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| `user_id` | BIGINT | - | Primary key, Telegram user ID |
| `daily_limit` | INTEGER | 10 | User's daily generation limit |
| `remaining` | INTEGER | 10 | User's remaining generations today |
| `last_reset` | DATE | CURRENT_DATE | Date of last reset |

**Features**:
- Auto-creates new users with default limits
- Individual limits can be customized per user
- Automatic daily reset functionality

#### 3. `cry_generation_logs`
**Purpose**: Audit trail and analytics

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| `id` | UUID | gen_random_uuid() | Primary key |
| `user_id` | BIGINT | - | User who made the request |
| `prompt` | TEXT | - | Generation prompt |
| `timestamp` | TIMESTAMPTZ | NOW() | When the request was made |
| `success` | BOOLEAN | - | Whether generation succeeded |
| `image_url` | TEXT | NULL | URL of generated image (if successful) |
| `error_message` | TEXT | NULL | Error details (if failed) |

**Features**:
- Complete audit trail of all generation attempts
- Success/failure tracking
- Prompt logging for analytics
- Automatic cleanup of logs older than 30 days

#### 4. `cry_user_id_mappings`
**Purpose**: Maps Supabase UUIDs to numeric IDs for compatibility

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| `id` | SERIAL | - | Auto-increment primary key |
| `supabase_uuid` | UUID | - | Supabase user UUID |
| `numeric_id` | BIGINT | - | Generated numeric ID |
| `created_at` | TIMESTAMPTZ | NOW() | Mapping creation time |

**Features**:
- Bridges Supabase auth with existing numeric ID system
- Ensures consistent user identification
- Handles UUID to numeric conversion safely

### Database Functions (RPC)

#### Core Rate Limiting Functions

##### `check_generation_allowed(user_id_param BIGINT)`
**Purpose**: Atomically checks both user and global limits

**Returns**: JSON object with:
```json
{
  "allowed": boolean,
  "user_remaining": number,
  "user_limit": number,
  "global_remaining": number,
  "global_limit": number,
  "reason": "allowed" | "user_limit_exceeded" | "global_limit_exceeded"
}
```

**Logic**:
1. Auto-resets user limit if new day
2. Auto-resets global limit if new day
3. Checks both limits have capacity
4. Returns comprehensive status

##### `decrement_both_limits(user_id_param BIGINT)`
**Purpose**: Atomically decrements both user and global limits

**Returns**: Boolean (success/failure)

**Logic**:
1. Verifies both limits have capacity
2. Decrements user remaining by 1
3. Decrements global remaining by 1
4. All operations are atomic (transaction)

##### `check_and_reset_user_daily_limit(user_id_param BIGINT)`
**Purpose**: Checks and resets user limit if new day

**Returns**: User limits record

**Logic**:
1. Creates user if doesn't exist
2. Resets limit if `last_reset < CURRENT_DATE`
3. Returns current user limits

##### `check_and_reset_global_daily_limit()`
**Purpose**: Checks and resets global limit if new day

**Returns**: Global settings record

**Logic**:
1. Resets limit if `last_reset < CURRENT_DATE`
2. Returns current global settings

##### `get_or_create_numeric_user_id(supabase_uuid UUID)`
**Purpose**: Maps Supabase UUID to numeric ID

**Returns**: BIGINT numeric ID

**Logic**:
1. Returns existing mapping if found
2. Generates new numeric ID from UUID
3. Handles collisions by incrementing
4. Creates and returns new mapping

### Row Level Security (RLS)

All tables have RLS enabled with the following policies:

#### Service Role Policies
- **Full access** to all tables for bot operations
- Required for automated limit checking and logging

#### Authenticated User Policies
- **Read access** to global settings (for admin panel)
- **Read access** to user limits (for admin panel)
- **Read access** to generation logs (for admin panel)
- **Manage access** to user limits (for admin operations)

#### Security Features
- No public access to any tables
- Service key required for bot operations
- Authenticated access required for admin panel
- Input validation and sanitization
- No sensitive data exposed in logs

## 🔧 Configuration

### Default Limits
- **Global Daily Limit**: 100 generations/day
- **User Daily Limit**: 10 generations/day
- **Reset Time**: Midnight UTC daily

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
OPENAI_API_KEY=your-openai-api-key
```

### Admin Configuration
Admins can modify limits through the admin panel:
- Adjust global daily limits
- Set custom user limits
- View usage analytics
- Monitor system health

## 📈 Monitoring & Analytics

### Key Metrics
- Global utilization percentage
- User limit exhaustion rates
- Generation success/failure rates
- Daily usage patterns
- System health status

### Monitoring Queries
```sql
-- System health check
SELECT 
    daily_limit,
    remaining,
    ROUND(((daily_limit - remaining) * 100.0 / daily_limit), 2) as utilization_percent
FROM cry_global_settings WHERE id = 1;

-- Users with depleted limits
SELECT user_id, remaining, daily_limit 
FROM cry_user_limits 
WHERE remaining = 0;

-- Daily generation statistics
SELECT 
    COUNT(*) as total_attempts,
    COUNT(CASE WHEN success = true THEN 1 END) as successful_generations
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE;
```

## 🚨 Emergency Operations

### Disable All Generation
```sql
UPDATE cry_global_settings SET remaining = 0 WHERE id = 1;
```

### Reset All Limits
```sql
UPDATE cry_global_settings SET remaining = daily_limit WHERE id = 1;
UPDATE cry_user_limits SET remaining = daily_limit;
```

### Clean Old Logs
```sql
DELETE FROM cry_generation_logs 
WHERE timestamp < CURRENT_DATE - INTERVAL '30 days';
```

## 🔄 Daily Operations

### Automatic Processes
- **Limit Reset**: Handled automatically by database functions
- **User Creation**: New users created on first generation attempt
- **Log Cleanup**: Manual cleanup recommended monthly

### Manual Maintenance
- Monitor global utilization
- Adjust limits based on usage patterns
- Review failed generation logs
- Clean up old audit logs

## 🛠️ Implementation Details

### API Integration
The rate limiting system integrates with:
- **Telegram Bot**: Uses numeric user IDs from Telegram
- **Web App**: Uses Supabase UUID to numeric ID mapping
- **Admin Panel**: Direct Supabase table management

### Error Handling
- Graceful degradation if database unavailable
- Comprehensive logging of all operations
- Atomic operations prevent data corruption
- Clear error messages for users

### Performance Optimizations
- Indexed queries on user_id and timestamp
- Single-row global settings for fast access
- Atomic operations minimize lock time
- Efficient UUID to numeric ID conversion

This dual-layer system ensures both system stability and fair user access while providing comprehensive monitoring and administrative control.
