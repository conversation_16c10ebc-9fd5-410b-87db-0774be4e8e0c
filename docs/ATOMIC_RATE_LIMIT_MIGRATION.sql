-- Atomic Rate Limiting Migration
-- This SQL function provides atomic check-and-decrement for rate limiting
-- to prevent race conditions in concurrent requests

-- Create atomic rate limit check function
CREATE OR REPLACE FUNCTION atomic_rate_limit_check(user_id_param INTEGER)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_limits RECORD;
    global_limits RECORD;
    result JSON;
BEGIN
    -- Begin transaction to ensure atomicity
    -- Get user limits with row-level lock to prevent race conditions
    SELECT user_remaining, user_limit 
    INTO user_limits
    FROM cry_rate_limits 
    WHERE user_id = user_id_param
    FOR UPDATE;
    
    -- If user doesn't exist, create with default limits
    IF NOT FOUND THEN
        INSERT INTO cry_rate_limits (user_id, user_remaining, user_limit)
        VALUES (user_id_param, 9, 10)  -- Start with 9 remaining (assuming 10 is the limit)
        RETURNING user_remaining, user_limit INTO user_limits;
    END IF;
    
    -- Get global limits with row-level lock
    SELECT global_remaining, global_limit
    INTO global_limits
    FROM cry_global_limits
    WHERE id = 1
    FOR UPDATE;
    
    -- If global limits don't exist, create them
    IF NOT FOUND THEN
        INSERT INTO cry_global_limits (id, global_remaining, global_limit)
        VALUES (1, 999, 1000)  -- Start with 999 remaining (assuming 1000 is the limit)
        RETURNING global_remaining, global_limit INTO global_limits;
    END IF;
    
    -- Check if request should be allowed
    IF user_limits.user_remaining > 0 AND global_limits.global_remaining > 0 THEN
        -- Decrement both limits atomically
        UPDATE cry_rate_limits 
        SET user_remaining = user_remaining - 1
        WHERE user_id = user_id_param;
        
        UPDATE cry_global_limits 
        SET global_remaining = global_remaining - 1
        WHERE id = 1;
        
        -- Return success with updated counts
        result := json_build_object(
            'allowed', true,
            'user_remaining', user_limits.user_remaining - 1,
            'user_limit', user_limits.user_limit,
            'global_remaining', global_limits.global_remaining - 1,
            'global_limit', global_limits.global_limit
        );
    ELSE
        -- Return failure without decrementing
        result := json_build_object(
            'allowed', false,
            'user_remaining', user_limits.user_remaining,
            'user_limit', user_limits.user_limit,
            'global_remaining', global_limits.global_remaining,
            'global_limit', global_limits.global_limit
        );
    END IF;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return safe failure
        RAISE LOG 'Error in atomic_rate_limit_check: %', SQLERRM;
        RETURN json_build_object(
            'allowed', false,
            'user_remaining', 0,
            'user_limit', 0,
            'global_remaining', 0,
            'global_limit', 0,
            'error', SQLERRM
        );
END;
$$;

-- Grant execute permission to the application user
GRANT EXECUTE ON FUNCTION atomic_rate_limit_check(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION atomic_rate_limit_check(INTEGER) TO service_role;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cry_rate_limits_user_id ON cry_rate_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_cry_global_limits_id ON cry_global_limits(id);

-- Create a function to reset daily limits (to be called by a cron job)
CREATE OR REPLACE FUNCTION reset_daily_rate_limits()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Reset user limits to maximum
    UPDATE cry_rate_limits SET user_remaining = user_limit;
    
    -- Reset global limits to maximum
    UPDATE cry_global_limits SET global_remaining = global_limit WHERE id = 1;
    
    RAISE LOG 'Daily rate limits reset at %', NOW();
END;
$$;

-- Grant execute permission for reset function
GRANT EXECUTE ON FUNCTION reset_daily_rate_limits() TO service_role;

-- Example cron job setup (run this separately or in your Supabase dashboard)
-- SELECT cron.schedule('reset-rate-limits', '0 0 * * *', 'SELECT reset_daily_rate_limits();');

COMMENT ON FUNCTION atomic_rate_limit_check(INTEGER) IS 'Atomically checks and decrements rate limits to prevent race conditions';
COMMENT ON FUNCTION reset_daily_rate_limits() IS 'Resets all rate limits to their maximum values, intended for daily cron execution';