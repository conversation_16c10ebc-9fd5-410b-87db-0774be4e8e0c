# Environment Variable Security Guide 🔒

## Overview

This document outlines the security measures implemented to prevent unauthorized environment variable exposure and ensure secure configuration management.

## Security Improvements ✅

### 1. Centralized Environment Configuration

- **File**: `/lib/envConfig.ts`
- **Purpose**: Centralized, type-safe environment variable management
- **Security Features**:
  - Client/server variable segregation
  - Runtime protection against client-side access to server variables
  - Validation functions for required variables
  - Type safety for all environment configurations

### 2. Client-Server Variable Separation

#### ✅ Safe Client-Side Variables (NEXT_PUBLIC_*)
```env
NEXT_PUBLIC_SUPABASE_URL=              # Supabase project URL (safe to expose)
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=  # Anon key (designed for client use)
NEXT_PUBLIC_SITE_URL=                  # Application URL (public information)
NEXT_PUBLIC_MAX_FILE_SIZE=             # File size limit (public config)
NEXT_PUBLIC_ALLOWED_TYPES=             # Allowed file types (public config)
```

#### 🔒 Server-Only Variables (NEVER exposed to client)
```env
SUPABASE_SERVICE_KEY=                  # Service role key (server-only)
OPENAI_API_KEY=                        # OpenAI API key (server-only)
OPENROUTER_API_KEY=                    # OpenRouter API key (server-only)
MODEL=                                 # AI model selection (server-only)
IMAGE_MODEL=                           # Image model selection (server-only)
```

### 3. Runtime Protection

- **Client-side proxy**: Prevents accidental access to server variables on client
- **Validation functions**: Ensure all required variables are present
- **Error handling**: Graceful degradation when variables are missing

## Implementation Details

### Environment Configuration Structure

```typescript
// Client environment (safe to expose)
export const clientEnv = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,
  },
  site: {
    url: process.env.NEXT_PUBLIC_SITE_URL || fallbackUrl,
  },
  upload: {
    maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'),
    allowedTypes: process.env.NEXT_PUBLIC_ALLOWED_TYPES?.split(',') || defaults,
  },
};

// Server environment (protected from client access)
export const serverEnv = {
  supabase: { serviceKey: process.env.SUPABASE_SERVICE_KEY! },
  openai: { apiKey: process.env.OPENAI_API_KEY! },
  openrouter: { apiKey: process.env.OPENROUTER_API_KEY! },
  model: process.env.MODEL || 'gpt-3.5-turbo',
  imageModel: process.env.IMAGE_MODEL || 'gpt-image-1',
  nodeEnv: process.env.NODE_ENV || 'development',
};
```

### Updated Files

1. **`/lib/envConfig.ts`** - New centralized configuration
2. **`/lib/utils.ts`** - Updated to use centralized config
3. **`/lib/supabase/client.ts`** - Uses clientEnv for Supabase config
4. **`/lib/supabase/server.ts`** - Uses clientEnv for Supabase config
5. **`/lib/supabase/middleware.ts`** - Uses clientEnv for Supabase config
6. **`/lib/rateLimiter.ts`** - Uses serverEnv for service key
7. **`/app/api/text-process/route.ts`** - Uses serverEnv for API keys
8. **`/app/api/process/route.ts`** - Uses serverEnv for API keys
9. **`/components/sign-up-form.tsx`** - Uses clientEnv for site URL
10. **`/components/forgot-password-form.tsx`** - Uses clientEnv for site URL
11. **`/lib/oauthSecurity.ts`** - Uses clientEnv for site URL validation

## Security Benefits

### ✅ Prevents Accidental Exposure
- Server-only variables are protected by runtime proxy
- TypeScript errors if attempting to access server vars on client
- Clear separation between client and server configurations

### ✅ Type Safety
- All environment variables are typed
- Required variables are enforced
- Default values are provided where appropriate

### ✅ Validation
- Environment validation functions ensure all required vars are present
- Graceful error handling for missing variables
- Development vs production environment detection

### ✅ Centralized Management
- Single source of truth for all environment configuration
- Easier to audit and maintain
- Consistent access patterns across the application

## Migration Guide

### Before (Insecure)
```typescript
// Direct access - no protection
const apiKey = process.env.OPENAI_API_KEY;
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'fallback';
```

### After (Secure)
```typescript
// Client-side
import { clientEnv } from '@/lib/envConfig';
const siteUrl = clientEnv.site.url;

// Server-side
import { serverEnv } from '@/lib/envConfig';
const apiKey = serverEnv.openai.apiKey;
```

## Environment Variables Reference

### Required Variables
```env
# Client-safe variables
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key

# Server-only variables
SUPABASE_SERVICE_KEY=your-service-key
OPENAI_API_KEY=your-openai-key
OPENROUTER_API_KEY=your-openrouter-key
```

### Optional Variables
```env
# Client-safe optional
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_TYPES=image/jpeg,image/png,image/webp

# Server-only optional
MODEL=gpt-3.5-turbo
IMAGE_MODEL=gpt-image-1
```

## Security Checklist ✅

- [x] Server-only variables are never exposed to client
- [x] Client variables are explicitly marked as safe
- [x] Runtime protection prevents accidental server var access
- [x] Type safety ensures correct variable usage
- [x] Validation functions check for required variables
- [x] Centralized configuration management
- [x] Clear documentation for all variables
- [x] Migration of all existing code to use centralized config

## Best Practices

1. **Always use centralized config**: Import from `/lib/envConfig.ts`
2. **Never directly access process.env**: Use clientEnv or serverEnv
3. **Validate environment on startup**: Call validation functions
4. **Document new variables**: Add to this guide and type definitions
5. **Use TypeScript**: Leverage type safety for environment variables

## Conclusion

The environment variable security implementation provides robust protection against accidental exposure of sensitive server-side configuration while maintaining type safety and ease of use. All environment variables are now properly segregated and protected according to security best practices.