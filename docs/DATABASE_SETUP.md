# Database Setup for Rate Limiting System

This document provides the SQL scripts needed to set up the rate limiting system in Supabase.

## Prerequisites

1. Access to your Supabase project dashboard
2. SQL Editor access in Supabase
3. Service role key configured in environment variables

## Environment Variables Required

Add this to your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-anon-key

# Supabase Service Key (for rate limiting and server-side operations)
SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here
```

**✅ Status**: All environment variables are properly configured in the current project.

## Step 1: Create Tables

**✅ Status**: All tables have been created and are operational in the Telegram project.

~~Run these SQL commands in the Supabase SQL Editor:~~ (Already completed)

### 1.1 Global Settings Table

```sql
-- Create global settings table
CREATE TABLE IF NOT EXISTS cry_global_settings (
    id INTEGER PRIMARY KEY DEFAULT 1,
    daily_limit INTEGER NOT NULL DEFAULT 100,
    remaining INTEGER NOT NULL DEFAULT 100,
    last_reset DATE NOT NULL DEFAULT CURRENT_DATE,
    CONSTRAINT single_row_constraint CHECK (id = 1)
);

-- Insert initial global settings
INSERT INTO cry_global_settings (id, daily_limit, remaining, last_reset)
VALUES (1, 100, 100, CURRENT_DATE)
ON CONFLICT (id) DO NOTHING;
```

### 1.2 User Limits Table

```sql
-- Create user limits table
CREATE TABLE IF NOT EXISTS cry_user_limits (
    user_id BIGINT PRIMARY KEY,
    daily_limit INTEGER NOT NULL DEFAULT 10,
    remaining INTEGER NOT NULL DEFAULT 10,
    last_reset DATE NOT NULL DEFAULT CURRENT_DATE
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_cry_user_limits_user_id ON cry_user_limits(user_id);
```

### 1.3 Generation Logs Table

```sql
-- Create generation logs table
CREATE TABLE IF NOT EXISTS cry_generation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id BIGINT NOT NULL,
    prompt TEXT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    success BOOLEAN NOT NULL,
    image_url TEXT,
    error_message TEXT
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cry_generation_logs_user_id ON cry_generation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_cry_generation_logs_timestamp ON cry_generation_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_cry_generation_logs_success ON cry_generation_logs(success);
```

### 1.4 User ID Mappings Table

```sql
-- Create user ID mappings table
CREATE TABLE IF NOT EXISTS cry_user_id_mappings (
    id SERIAL PRIMARY KEY,
    supabase_uuid UUID NOT NULL UNIQUE,
    numeric_id BIGINT NOT NULL UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cry_user_id_mappings_uuid ON cry_user_id_mappings(supabase_uuid);
CREATE INDEX IF NOT EXISTS idx_cry_user_id_mappings_numeric ON cry_user_id_mappings(numeric_id);
```

## Step 2: Create RPC Functions

### 2.1 User ID Mapping Function

```sql
-- Function to get or create numeric user ID from Supabase UUID
CREATE OR REPLACE FUNCTION get_or_create_numeric_user_id(supabase_uuid UUID)
RETURNS BIGINT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    existing_id BIGINT;
    new_id BIGINT;
BEGIN
    -- Try to find existing mapping
    SELECT numeric_id INTO existing_id
    FROM cry_user_id_mappings
    WHERE supabase_uuid = $1;
    
    IF existing_id IS NOT NULL THEN
        RETURN existing_id;
    END IF;
    
    -- Generate new numeric ID from UUID
    new_id := (('x' || substr(replace($1::text, '-', ''), 1, 15))::bit(60)::bigint);
    
    -- Handle potential collisions by incrementing
    WHILE EXISTS (SELECT 1 FROM cry_user_id_mappings WHERE numeric_id = new_id) LOOP
        new_id := new_id + 1;
    END LOOP;
    
    -- Insert new mapping
    INSERT INTO cry_user_id_mappings (supabase_uuid, numeric_id)
    VALUES ($1, new_id);
    
    RETURN new_id;
END;
$$;
```

### 2.2 Daily Limit Reset Functions

```sql
-- Function to check and reset user daily limit
CREATE OR REPLACE FUNCTION check_and_reset_user_daily_limit(user_id_param BIGINT)
RETURNS TABLE(user_id BIGINT, daily_limit INTEGER, remaining INTEGER, last_reset DATE)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Create user if doesn't exist or reset if new day
    INSERT INTO cry_user_limits (user_id, daily_limit, remaining, last_reset)
    VALUES (user_id_param, 10, 10, CURRENT_DATE)
    ON CONFLICT (user_id) DO UPDATE SET
        daily_limit = CASE 
            WHEN cry_user_limits.last_reset < CURRENT_DATE THEN cry_user_limits.daily_limit
            ELSE cry_user_limits.daily_limit
        END,
        remaining = CASE 
            WHEN cry_user_limits.last_reset < CURRENT_DATE THEN cry_user_limits.daily_limit
            ELSE cry_user_limits.remaining
        END,
        last_reset = CASE 
            WHEN cry_user_limits.last_reset < CURRENT_DATE THEN CURRENT_DATE
            ELSE cry_user_limits.last_reset
        END;
    
    RETURN QUERY
    SELECT ul.user_id, ul.daily_limit, ul.remaining, ul.last_reset
    FROM cry_user_limits ul
    WHERE ul.user_id = user_id_param;
END;
$$;

-- Function to check and reset global daily limit
CREATE OR REPLACE FUNCTION check_and_reset_global_daily_limit()
RETURNS TABLE(id INTEGER, daily_limit INTEGER, remaining INTEGER, last_reset DATE)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Reset global limit if new day
    UPDATE cry_global_settings
    SET remaining = daily_limit,
        last_reset = CURRENT_DATE
    WHERE id = 1 AND last_reset < CURRENT_DATE;
    
    RETURN QUERY
    SELECT gs.id, gs.daily_limit, gs.remaining, gs.last_reset
    FROM cry_global_settings gs
    WHERE gs.id = 1;
END;
$$;
```

### 2.3 Core Rate Limiting Functions

```sql
-- Function to check if generation is allowed
CREATE OR REPLACE FUNCTION check_generation_allowed(user_id_param BIGINT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_data RECORD;
    global_data RECORD;
    result JSON;
BEGIN
    -- Reset and get user limits
    SELECT * INTO user_data FROM check_and_reset_user_daily_limit(user_id_param);
    
    -- Reset and get global limits
    SELECT * INTO global_data FROM check_and_reset_global_daily_limit();
    
    -- Build result
    result := json_build_object(
        'allowed', (user_data.remaining > 0 AND global_data.remaining > 0),
        'user_remaining', user_data.remaining,
        'user_limit', user_data.daily_limit,
        'global_remaining', global_data.remaining,
        'global_limit', global_data.daily_limit
    );
    
    RETURN result;
END;
$$;

-- Function to decrement both limits atomically
CREATE OR REPLACE FUNCTION decrement_both_limits(user_id_param BIGINT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_remaining INTEGER;
    global_remaining INTEGER;
BEGIN
    -- Start transaction
    BEGIN
        -- Check and decrement user limit
        UPDATE cry_user_limits
        SET remaining = remaining - 1
        WHERE user_id = user_id_param AND remaining > 0
        RETURNING remaining INTO user_remaining;
        
        IF user_remaining IS NULL THEN
            RETURN FALSE;
        END IF;
        
        -- Check and decrement global limit
        UPDATE cry_global_settings
        SET remaining = remaining - 1
        WHERE id = 1 AND remaining > 0
        RETURNING remaining INTO global_remaining;
        
        IF global_remaining IS NULL THEN
            -- Rollback user decrement
            UPDATE cry_user_limits
            SET remaining = remaining + 1
            WHERE user_id = user_id_param;
            RETURN FALSE;
        END IF;
        
        RETURN TRUE;
    EXCEPTION WHEN OTHERS THEN
        -- Rollback on any error
        RETURN FALSE;
    END;
END;
$$;
```

## Step 3: Set Up Row Level Security (RLS)

```sql
-- Enable RLS on all tables
ALTER TABLE cry_global_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE cry_user_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE cry_generation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE cry_user_id_mappings ENABLE ROW LEVEL SECURITY;

-- Service role policies (full access for bot operations)
CREATE POLICY "Service role full access" ON cry_global_settings FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON cry_user_limits FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON cry_generation_logs FOR ALL USING (auth.role() = 'service_role');
CREATE POLICY "Service role full access" ON cry_user_id_mappings FOR ALL USING (auth.role() = 'service_role');

-- Authenticated user policies (read access for admin panel)
CREATE POLICY "Authenticated read access" ON cry_global_settings FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON cry_user_limits FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Authenticated read access" ON cry_generation_logs FOR SELECT USING (auth.role() = 'authenticated');
```

## Step 4: Verification

Run these queries to verify the setup:

```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'cry_%';

-- Check functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%user_id%' OR routine_name LIKE '%limit%';

-- Test the system
SELECT get_or_create_numeric_user_id('550e8400-e29b-41d4-a716-************'::uuid);
SELECT check_generation_allowed(123456789);
```

## Troubleshooting

1. **Permission Errors**: Ensure your service role key is correct
2. **Function Errors**: Check the Supabase logs for detailed error messages
3. **RLS Issues**: Verify policies are created and enabled

## Monitoring

Use these queries to monitor the system:

```sql
-- Check global utilization
SELECT daily_limit, remaining, 
       ROUND(((daily_limit - remaining) * 100.0 / daily_limit), 2) as utilization_percent
FROM cry_global_settings WHERE id = 1;

-- Check users with depleted limits
SELECT user_id, remaining, daily_limit 
FROM cry_user_limits 
WHERE remaining = 0;

-- Daily generation statistics
SELECT COUNT(*) as total_attempts,
       COUNT(CASE WHEN success = true THEN 1 END) as successful_generations
FROM cry_generation_logs 
WHERE timestamp >= CURRENT_DATE;
```
