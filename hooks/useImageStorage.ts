import { useState, useEffect, useCallback } from 'react';
import {
  StoredImage,
  storeProcessedImage,
  getStoredImages,
  getImageById,
  clearAllImages,
  getStorageStats,
  isStorageAvailable
} from '@/lib/imageStorage';

// Hook for managing stored images
export function useStoredImages(userId?: string) {
  const [images, setImages] = useState<StoredImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load stored images
  const loadImages = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const available = await isStorageAvailable();
      if (!available) {
        setError('Local storage is not available');
        return;
      }

      const storedImages = await getStoredImages(userId);
      setImages(storedImages);
      console.log(`🔄 Loaded ${storedImages.length} stored images`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load images';
      setError(errorMessage);
      console.error('❌ Error loading stored images:', err);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Store a new image
  const storeImage = useCallback(async (
    originalImageUrl: string,
    processedImageUrl: string,
    originalFileName: string,
    prompt: string,
    model?: string
  ) => {
    try {
      setError(null);
      
      await storeProcessedImage(
        originalImageUrl,
        processedImageUrl,
        originalFileName,
        prompt,
        model,
        userId // Pass user ID for encryption
      );
      
      // Reload images to reflect the new addition
      await loadImages();
      
      console.log('✅ Image stored and list refreshed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to store image';
      setError(errorMessage);
      console.error('❌ Error storing image:', err);
      throw err; // Re-throw so calling component can handle it
    }
  }, [loadImages, userId]);

  // Clear all stored images
  const clearImages = useCallback(async () => {
    try {
      setError(null);
      await clearAllImages();
      setImages([]);
      console.log('🗑️ All images cleared');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear images';
      setError(errorMessage);
      console.error('❌ Error clearing images:', err);
      throw err;
    }
  }, []);

  // Load images on mount
  useEffect(() => {
    loadImages();
  }, [loadImages]);

  return {
    images,
    loading,
    error,
    storeImage,
    clearImages,
    refreshImages: loadImages
  };
}

// Hook for getting a specific image
export function useStoredImage(imageId: string | null, userId?: string) {
  const [image, setImage] = useState<StoredImage | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadImage = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const storedImage = await getImageById(id, userId);
      setImage(storedImage);
      
      if (storedImage) {
        console.log(`👁️ Loaded image: ${id}`);
      } else {
        console.log(`❓ Image not found: ${id}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load image';
      setError(errorMessage);
      console.error('❌ Error loading image:', err);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (imageId) {
      loadImage(imageId);
    } else {
      setImage(null);
      setError(null);
    }
  }, [imageId, loadImage]);

  return {
    image,
    loading,
    error
  };
}

// Hook for storage statistics
export function useStorageStats() {
  const [stats, setStats] = useState({
    count: 0,
    totalSize: 0,
    formattedSize: '0 B'
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const storageStats = await getStorageStats();
      setStats(storageStats);
      console.log('📊 Storage stats loaded:', storageStats);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load storage stats';
      setError(errorMessage);
      console.error('❌ Error loading storage stats:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return {
    stats,
    loading,
    error,
    refreshStats: loadStats
  };
}

// Hook for checking storage availability
export function useStorageAvailability() {
  const [available, setAvailable] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAvailability = async () => {
      try {
        const isAvailable = await isStorageAvailable();
        setAvailable(isAvailable);
        console.log(`💾 Storage availability: ${isAvailable ? 'Available' : 'Not available'}`);
      } catch (err) {
        setAvailable(false);
        console.error('❌ Error checking storage availability:', err);
      } finally {
        setLoading(false);
      }
    };

    checkAvailability();
  }, []);

  return {
    available,
    loading
  };
}
